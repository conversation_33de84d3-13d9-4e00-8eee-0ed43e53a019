import "geometry_types.fl" show Point, Rectangle, Circle, Drawable, Measurable;
import "data_structures.fl" as ds;

fun demo_geometry(): void {
    print("=== Geometry Demo ===");
    
    let point = Point::new(10, 20);
    point.draw();
    
    let rect = Rectangle::new(5, 8);
    rect.draw();
    print("Rectangle area: " + rect.area().to_string());
    print("Rectangle perimeter: " + rect.perimeter().to_string());
    
    let circle = Circle::new(3);
    circle.draw();
    print("Circle area: " + circle.area().to_string());
    print("Circle perimeter: " + circle.perimeter().to_string());
}

fun demo_data_structures(): void {
    print("=== Data Structures Demo ===");
    
    let stack = ds.Stack<string>::new();
    stack.push("First");
    stack.push("Second");
    stack.push("Third");
    
    print("Stack size: " + stack.size().to_string());
    print("Popping from stack: " + stack.pop());
    print("Popping from stack: " + stack.pop());
    print("Stack size after popping: " + stack.size().to_string());
    
    let queue = ds.Queue<num>::new();
    queue.push(1);
    queue.push(2);
    queue.push(3);
    
    print("Queue size: " + queue.size().to_string());
    print("Dequeuing from queue: " + queue.pop().to_string());
    print("Dequeuing from queue: " + queue.pop().to_string());
    print("Queue size after dequeuing: " + queue.size().to_string());
}

fun demo_polymorphism(): void {
    print("=== Polymorphism Demo ===");
    
    let shapes = Array<Drawable>();
    shapes.add(Point::new(1, 2));
    shapes.add(Rectangle::new(4, 6));
    shapes.add(Circle::new(2.5));
    
    print("Drawing all shapes:");
    let i = 0;
    while i < shapes.length() {
        let shape = shapes.get(i);
        shape.draw();
        i = i + 1;
    }
}

fun main(): void {
    print("=== Struct and Interface Import/Export Demo ===");
    
    demo_geometry();
    print("");
    demo_data_structures();
    print("");
    demo_polymorphism();
    
    print("=== Demo Complete ===");
}

main();
