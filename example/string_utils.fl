fun format_name(first: string, last: string): string {
    return first + " " + last;
}

fun create_greeting(name: string): string {
    return "Hello, " + name + "!";
}

fun repeat_string(text: string, count: num): string {
    return text.repeat(count);
}

fun is_valid_email(email: string): bool {
    return email.contains("@") && email.contains(".");
}

export { format_name, create_greeting, repeat_string, is_valid_email };
