fun main(): void {
    print("=== String Manipulation Demo (snake_case) ===");
    
    let text = "Hello, Fluent Lang!";
    print("Original text: " + text);
    print("Length: " + text.length().to_string());
    
    print("--- Substring Operations ---");
    print("First 5 chars: " + text.substring(0, 5));
    print("From index 7: " + text.substring(7));
    
    print("--- Search Operations ---");
    print("Index of 'Fluent': " + text.index_of("Fluent").to_string());
    print("Index of 'o': " + text.index_of("o").to_string());
    print("Index of 'xyz': " + text.index_of("xyz").to_string());
    
    print("--- Case Operations ---");
    print("Uppercase: " + text.to_upper_case());
    print("Lowercase: " + text.to_lower_case());
    
    print("--- Replace Operations ---");
    print("Replace 'Fluent' with 'Amazing': " + text.replace("Fluent", "Amazing"));
    print("Replace 'o' with '0': " + text.replace("o", "0"));
    
    print("--- <PERSON><PERSON><PERSON> Checks ---");
    print("Contains 'Fluent': " + text.contains("Fluent").to_string());
    print("Starts with 'Hello': " + text.starts_with("Hello").to_string());
    print("Ends with 'Lang!': " + text.ends_with("Lang!").to_string());
    print("Contains 'Python': " + text.contains("Python").to_string());
    
    print("--- Character Access ---");
    print("First character: " + text.char_at(0));
    print("Last character: " + text.char_at(text.length() - 1));
    
    print("--- String Manipulation ---");
    let greeting = "Hi";
    print("Repeat 'Hi' 3 times: " + greeting.repeat(3));
    
    let name = "John";
    print("Pad left with spaces: '" + name.pad_left(10) + "'");
    print("Pad right with stars: '" + name.pad_right(10, "*") + "'");
    
    print("--- Trim Operations ---");
    let messy = "  Hello World  ";
    print("Before trim: '" + messy + "'");
    print("After trim: '" + messy.trim() + "'");
    
    print("--- Empty Checks ---");
    let empty = "";
    let notEmpty = "Content";
    print("Empty string is_empty(): " + empty.is_empty().to_string());
    print("Non-empty string is_empty(): " + notEmpty.is_empty().to_string());
    print("Empty string is_not_empty(): " + empty.is_not_empty().to_string());
    print("Non-empty string is_not_empty(): " + notEmpty.is_not_empty().to_string());
    
    print("--- Split Operations ---");
    let csv = "apple,banana,cherry,date";
    let fruits = csv.split(",");
    print("Split CSV: " + fruits);
    
    let sentence = "The quick brown fox";
    let words = sentence.split(" ");
    print("Split sentence: " + words);
    
    print("--- Practical Example: Email Validation Helper ---");
    let email = "<EMAIL>";
    print("Email: " + email);
    print("Contains '@': " + email.contains("@").to_string());
    print("Ends with '.com': " + email.ends_with(".com").to_string());
    
    let atIndex = email.index_of("@");
    if atIndex > 0 {
        let username = email.substring(0, atIndex);
        let domain = email.substring(atIndex + 1);
        print("Username: " + username);
        print("Domain: " + domain);
    }
    
    print("--- String Building Example ---");
    let firstName = "John";
    let lastName = "Doe";
    let fullName = firstName + " " + lastName;
    print("Full name: " + fullName);
    print("Initials: " + firstName.char_at(0) + "." + lastName.char_at(0) + ".");
    
    print("=== String Manipulation Demo Complete ===");
}

main();
