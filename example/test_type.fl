struct Array<T> {
    items: T?;
    next: Array<T>?;
}

interface IArray<T> {
    fun push(item: T): void;
    fun pop(): T;
    fun size(): num;
}

Array<T>: IArray<T> {
    static fun new(): Array<T> {
        return Array { items: null, next: null };
    }

    fun push(item: T): void {
        let oldItems = this.items;
        let oldNext = this.next;

        let newNode = Array::new();
        newNode.items = oldItems;
        newNode.next = oldNext;

        this.items = item;
        this.next = newNode;
    }

    fun pop(): T {
        let item = this.items;
        if item == null {
            panic("Cannot pop empty array");
        }
        if this.next != null {
            this.items = this.next.items;
            this.next = this.next.next;
        } else {
            this.items = null;
            this.next = null;
        }

        return item;
    }

    fun size(): num {
        if this.items == null {
            return 0;
        } else {
            if this.next == null {
                return 1;
            } else {
                return 1 + this.next.size();
            }
        }
    }
}

enum TodoStatus {
    Pending,
    Completed,
    Deleted
}

struct Todo {
    id: num;
    title: string;
    status: TodoStatus;
    completed: bool;
}

struct Todos {
    todos: Array<Todo>;
}


fun main(): void {
    let todos = Todos { todos: Array::new() };
    todos.todos.push(Todo { id: 1, title: "Learn Fluent", completed: false, status: TodoStatus::Pending });
    todos.todos.push(Todo { id: 2, title: "Learn Dart", completed: false, status: TodoStatus::Pending });
    todos.todos.push(Todo { id: 3, title: "Learn Flutter", completed: false, status: TodoStatus::Completed });

    print(todos.todos.size());
    print(todos.todos.pop().title);
    print(todos.todos.size());
}

main();
