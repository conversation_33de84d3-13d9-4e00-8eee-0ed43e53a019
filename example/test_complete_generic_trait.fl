struct Array<T> {
    items: T?;
    next: Array<T>?;
}

interface IArray<T> {
    fun push(item: T): void;
    fun pop(): T;
    fun size(): num;
}

Array<T>: IArray<T> {
    static fun new(): Array<T> {
        return Array { items: null, next: null };
    }

    fun push(item: T): void {
        let oldItems = this.items;
        let oldNext = this.next;

        let newNode = Array::new();
        newNode.items = oldItems;
        newNode.next = oldNext;

        this.items = item;
        this.next = newNode;
    }

    fun size(): num {
        if this.items == null {
            return 0;
        } else {
            if this.next == null {
                return 1;
            } else {
                return 1 + this.next.size();
            }
        }
    }
}

enum Status {
    Active,
    Inactive,
    Pending
}

struct Item<T> {
    value: T;
    status: Status;
}

let numbers = Array::new();
numbers.push(1);
numbers.push(2);
numbers.push(3);

print("Array size:");
print(numbers.size());

let item = Item { value: "test", status: Status::Active };
print("Item value:");
print(item.value);
print("Item status:");
print(item.status);
