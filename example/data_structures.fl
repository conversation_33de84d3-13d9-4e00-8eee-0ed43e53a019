struct Stack<T> {
    items: Array<T>;
}

struct Queue<T> {
    items: Array<T>;
}

interface Container<T> {
    fun push(item: T): void;
    fun pop(): T;
    fun size(): num;
    fun is_empty(): bool;
}

enum DataStructureType {
    StackType,
    QueueType,
    ListType
}

Stack<T>: Container<T> {
    fun new(): Stack<T> {
        let items = Array<T>();
        return Stack<T> { items: items };
    }
    
    fun push(item: T): void {
        this.items.add(item);
    }
    
    fun pop(): T {
        if this.items.length() == 0 {
            panic("Stack is empty");
        }
        let index = this.items.length() - 1;
        let item = this.items.get(index);
        this.items.removeAt(index);
        return item;
    }
    
    fun size(): num {
        return this.items.length();
    }
    
    fun is_empty(): bool {
        return this.items.length() == 0;
    }
}

Queue<T>: Container<T> {
    fun new(): Queue<T> {
        let items = Array<T>();
        return Queue<T> { items: items };
    }
    
    fun push(item: T): void {
        this.items.add(item);
    }
    
    fun pop(): T {
        if this.items.length() == 0 {
            panic("Queue is empty");
        }
        let item = this.items.get(0);
        this.items.removeAt(0);
        return item;
    }
    
    fun size(): num {
        return this.items.length();
    }
    
    fun is_empty(): bool {
        return this.items.length() == 0;
    }
}

export { Stack, Queue, Container, DataStructureType };
