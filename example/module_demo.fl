import { add, multiply } from "math_module.fl";
import { format_name, create_greeting } from "string_utils.fl";

fun calculate_area(width: num, height: num): num {
    return multiply(width, height);
}

fun greet_user(first_name: string, last_name: string): void {
    let full_name = format_name(first_name, last_name);
    let greeting = create_greeting(full_name);
    print(greeting);
}

fun main(): void {
    print("=== Module Demo ===");
    
    let result = add(10, 5);
    print("10 + 5 = " + result.to_string());
    
    let area = calculate_area(4, 6);
    print("Area of 4x6 rectangle = " + area.to_string());
    
    greet_user("<PERSON>", "Doe");
    
    print("=== Module Demo Complete ===");
}

main();
