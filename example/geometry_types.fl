struct Point {
    x: num;
    y: num;
}

struct Rectangle {
    width: num;
    height: num;
}

struct Circle {
    radius: num;
}

interface Drawable {
    fun draw(): void;
}

interface Measurable {
    fun area(): num;
    fun perimeter(): num;
}

enum Shape {
    PointShape,
    RectangleShape,
    CircleShape
}

Point: Drawable {
    fun new(x: num, y: num): Point {
        return Point { x: x, y: y };
    }
    
    fun draw(): void {
        print("Drawing point at (" + this.x.to_string() + ", " + this.y.to_string() + ")");
    }
}

Rectangle: Drawable {
    fun new(width: num, height: num): Rectangle {
        return Rectangle { width: width, height: height };
    }
    
    fun draw(): void {
        print("Drawing rectangle " + this.width.to_string() + "x" + this.height.to_string());
    }
}

Rectangle: Measurable {
    fun area(): num {
        return this.width * this.height;
    }
    
    fun perimeter(): num {
        return 2 * (this.width + this.height);
    }
}

Circle: Drawable {
    fun new(radius: num): Circle {
        return Circle { radius: radius };
    }
    
    fun draw(): void {
        print("Drawing circle with radius " + this.radius.to_string());
    }
}

Circle: Measurable {
    fun area(): num {
        return 3.14159 * this.radius * this.radius;
    }
    
    fun perimeter(): num {
        return 2 * 3.14159 * this.radius;
    }
}

export { Point, Rectangle, Circle, Drawable, Measurable, Shape };
