// String Manipulation Examples in Fluent Lang

fun main(): void {
    print("=== String Manipulation Examples ===");
    
    // Basic string operations
    let text = "Hello, Fluent Lang!";
    print("Original text: " + text);
    print("Length: " + text.length());
    
    // Substring operations
    print("\n--- Substring Operations ---");
    print("First 5 chars: " + text.substring(0, 5));
    print("From index 7: " + text.substring(7));
    
    // Search operations
    print("\n--- Search Operations ---");
    print("Index of 'Fluent': " + text.indexOf("Fluent"));
    print("Index of 'o': " + text.indexOf("o"));
    print("Index of 'xyz': " + text.indexOf("xyz"));
    
    // Case operations
    print("\n--- Case Operations ---");
    print("Uppercase: " + text.toUpperCase());
    print("Lowercase: " + text.toLowerCase());
    
    // Replace operations
    print("\n--- Replace Operations ---");
    print("Replace 'Fluent' with 'Amazing': " + text.replace("Fluent", "Amazing"));
    print("Replace 'o' with '0': " + text.replace("o", "0"));
    
    // Boolean checks
    print("\n--- Boolean Checks ---");
    print("Contains 'Fluent': " + text.contains("Fluent"));
    print("Starts with 'Hello': " + text.startsWith("Hello"));
    print("Ends with 'Lang!': " + text.endsWith("Lang!"));
    print("Contains 'Python': " + text.contains("Python"));
    
    // Character access
    print("\n--- Character Access ---");
    print("First character: " + text.charAt(0));
    print("Last character: " + text.charAt(text.length() - 1));
    
    // String manipulation
    print("\n--- String Manipulation ---");
    let greeting = "Hi";
    print("Repeat 'Hi' 3 times: " + greeting.repeat(3));
    
    let name = "John";
    print("Pad left with spaces: '" + name.padLeft(10) + "'");
    print("Pad right with stars: '" + name.padRight(10, "*") + "'");
    
    // Trim operations
    print("\n--- Trim Operations ---");
    let messy = "  Hello World  ";
    print("Before trim: '" + messy + "'");
    print("After trim: '" + messy.trim() + "'");
    
    // Empty checks
    print("\n--- Empty Checks ---");
    let empty = "";
    let notEmpty = "Content";
    print("Empty string isEmpty(): " + empty.isEmpty());
    print("Non-empty string isEmpty(): " + notEmpty.isEmpty());
    print("Empty string isNotEmpty(): " + empty.isNotEmpty());
    print("Non-empty string isNotEmpty(): " + notEmpty.isNotEmpty());
    
    // Split operations
    print("\n--- Split Operations ---");
    let csv = "apple,banana,cherry,date";
    let fruits = csv.split(",");
    print("Split CSV: " + fruits);
    
    let sentence = "The quick brown fox";
    let words = sentence.split(" ");
    print("Split sentence: " + words);
    
    // Practical example: Email validation helper
    print("\n--- Practical Example: Email Validation Helper ---");
    let email = "<EMAIL>";
    print("Email: " + email);
    print("Contains '@': " + email.contains("@"));
    print("Ends with '.com': " + email.endsWith(".com"));
    
    let atIndex = email.indexOf("@");
    if atIndex > 0 {
        let username = email.substring(0, atIndex);
        let domain = email.substring(atIndex + 1);
        print("Username: " + username);
        print("Domain: " + domain);
    }
    
    // String building example
    print("\n--- String Building Example ---");
    let firstName = "John";
    let lastName = "Doe";
    let fullName = firstName + " " + lastName;
    print("Full name: " + fullName);
    print("Initials: " + firstName.charAt(0) + "." + lastName.charAt(0) + ".");
    
    print("\n=== String Manipulation Examples Complete ===");
}

main();
