import 'package:fluent_lang/core/parser.dart';
import 'package:fluent_lang/core/compiler.dart';
import 'package:fluent_lang/core/virtual_machine.dart';

void main() {
  print('=== Test String Naming Convention (snake_case) ===');
  
  testSnakeCaseWorks();
  testCamelCaseFails();
}

void testSnakeCaseWorks() {
  print('\n--- Test 1: snake_case methods work ---');
  
  final code = '''
fun main(): void {
  let text = "Hello World";
  print(text.to_upper_case());
  print(text.starts_with("Hello"));
  print(text.char_at(0));
  print(text.is_empty());
  print(text.pad_left(15));
}

main();
''';

  runTest(code, 'snake_case methods');
}

void testCamelCaseFails() {
  print('\n--- Test 2: camelCase methods should fail ---');
  
  final camelCaseMethods = [
    'toUpperCase',
    'startsWith', 
    'charAt',
    'isEmpty',
    'padLeft'
  ];
  
  for (final method in camelCaseMethods) {
    final code = '''
fun main(): void {
  let text = "Hello World";
  print(text.$method());
}

main();
''';
    
    try {
      final parser = buildParser(verbose: false);
      final ast = parser.parse(code);
      
      if (ast.isFailure) {
        print('✅ $method - Parse failed as expected');
        continue;
      }
      
      final compiler = Compiler();
      final bytecode = compiler.compile(ast.value);
      
      final vm = VirtualMachine();
      vm.load(bytecode);
      vm.run();
      
      print('❌ $method - Should have failed but succeeded');
    } catch (e) {
      if (e.toString().contains('Unknown string method')) {
        print('✅ $method - Failed as expected: Unknown method');
      } else {
        print('✅ $method - Failed with error: ${e.toString().split('\n')[0]}');
      }
    }
  }
}

void runTest(String code, String testName) {
  try {
    final parser = buildParser(verbose: false);
    final ast = parser.parse(code);
    
    if (ast.isFailure) {
      print('❌ $testName - Parse Error: ${ast.message}');
      return;
    }
    
    final compiler = Compiler();
    final bytecode = compiler.compile(ast.value);
    
    final vm = VirtualMachine();
    vm.load(bytecode);
    vm.run();
    
    print('✅ $testName - Success');
  } catch (e) {
    print('❌ $testName - Error: $e');
  }
}
