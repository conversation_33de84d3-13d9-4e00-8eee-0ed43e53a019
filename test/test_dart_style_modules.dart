import 'package:fluent_lang/core/parser.dart';
import 'package:fluent_lang/core/compiler.dart';
import 'package:fluent_lang/core/virtual_machine.dart';

void main() {
  print('=== Test Dart-Style Module System ===');
  
  testSimpleImport();
  testPrefixImport();
  testShowImport();
  testReExport();
  testCircularDependencyDetection();
}

void testSimpleImport() {
  print('\n--- Test 1: Simple Import (Dart-style) ---');
  
  final code = '''
import "math.fl";

fun main(): void {
  print("Simple import works");
}

main();
''';

  runTest(code, 'Simple Import');
}

void testPrefixImport() {
  print('\n--- Test 2: Prefix Import (as alias) ---');
  
  final code = '''
import "math.fl" as math;

fun main(): void {
  print("Prefix import works");
}

main();
''';

  runTest(code, 'Prefix Import');
}

void testShowImport() {
  print('\n--- Test 3: Show Import (specific items) ---');
  
  final code = '''
import "math.fl" show add, subtract;

fun main(): void {
  print("Show import works");
}

main();
''';

  runTest(code, 'Show Import');
}

void testShowImportWithAlias() {
  print('\n--- Test 4: Show Import with Alias ---');
  
  final code = '''
import "math.fl" show add as plus, subtract as minus;

fun main(): void {
  print("Show import with alias works");
}

main();
''';

  runTest(code, 'Show Import with Alias');
}

void testReExport() {
  print('\n--- Test 5: Re-export ---');
  
  final code = '''
fun helper(): void {
  print("Helper function");
}

export { helper } from "utils.fl";
export { add, subtract };

fun main(): void {
  print("Re-export works");
}

main();
''';

  runTest(code, 'Re-export');
}

void testCircularDependencyDetection() {
  print('\n--- Test 6: Circular Dependency Detection ---');
  
  // This should parse and compile fine, but fail at runtime
  final code = '''
import "circular_a.fl";

fun main(): void {
  print("This should detect circular dependency");
}

main();
''';

  runTest(code, 'Circular Dependency Detection');
}

void testDeferredImport() {
  print('\n--- Test 7: Deferred Import ---');
  
  final code = '''
deferred import "heavy_module.fl" as heavy;

fun main(): void {
  print("Deferred import works");
}

main();
''';

  runTest(code, 'Deferred Import');
}

void testHideImport() {
  print('\n--- Test 8: Hide Import ---');
  
  final code = '''
import "math.fl" hide internal_function;

fun main(): void {
  print("Hide import works");
}

main();
''';

  runTest(code, 'Hide Import');
}

void runTest(String code, String testName) {
  try {
    final parser = buildParser(verbose: false);
    final ast = parser.parse(code);
    
    if (ast.isFailure) {
      print('❌ $testName - Parse Error: ${ast.message}');
      return;
    }
    
    final compiler = Compiler();
    final bytecode = compiler.compile(ast.value);
    
    final vm = VirtualMachine();
    vm.load(bytecode);
    vm.runAsync();
    
    print('✅ $testName - Success');
  } catch (e) {
    if (e.toString().contains('Module not found') || 
        e.toString().contains('Circular dependency')) {
      print('✅ $testName - Expected Error: ${e.toString().split('\n')[0]}');
    } else {
      print('❌ $testName - Unexpected Error: $e');
    }
  }
}
