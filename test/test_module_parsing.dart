import 'package:fluent_lang/core/parser.dart';
import 'package:fluent_lang/core/compiler.dart';
import 'package:fluent_lang/core/virtual_machine.dart';

void main() {
  print('=== Test Module Parsing and Compilation ===');
  
  testSimpleImport();
  testNamedImport();
  testExport();
  testImportExportCompilation();
}

void testSimpleImport() {
  print('\n--- Test 1: Simple Import Parsing ---');
  
  final code = '''
import "math.fl";

fun main(): void {
  print("Hello");
}

main();
''';

  runTest(code, 'Simple Import');
}

void testNamedImport() {
  print('\n--- Test 2: Named Import Parsing ---');
  
  final code = '''
import { add, subtract } from "math.fl";

fun main(): void {
  print("Hello");
}

main();
''';

  runTest(code, 'Named Import');
}

void testExport() {
  print('\n--- Test 3: Export Statement ---');
  
  final code = '''
fun add(a: num, b: num): num {
  return a + b;
}

fun subtract(a: num, b: num): num {
  return a - b;
}

export { add, subtract };

fun main(): void {
  print("Math module loaded");
}

main();
''';

  runTest(code, 'Export Statement');
}

void testImportExportCompilation() {
  print('\n--- Test 4: Import/Export Compilation ---');
  
  final code = '''
import { helper } from "utils.fl";

fun greet(name: string): void {
  print("Hello " + name);
}

export { greet };

fun main(): void {
  greet("World");
}

main();
''';

  runTest(code, 'Import/Export Compilation');
}

void runTest(String code, String testName) {
  try {
    final parser = buildParser(verbose: false);
    final ast = parser.parse(code);
    
    if (ast.isFailure) {
      print('❌ $testName - Parse Error: ${ast.message}');
      return;
    }
    
    final compiler = Compiler();
    final bytecode = compiler.compile(ast.value);
    
    final vm = VirtualMachine();
    vm.load(bytecode);
    vm.run();
    
    print('✅ $testName - Success');
  } catch (e) {
    print('❌ $testName - Error: $e');
  }
}
