import 'package:fluent_lang/core/parser.dart';
import 'package:fluent_lang/core/compiler.dart';
import 'package:fluent_lang/core/virtual_machine.dart';

void main() {
  print('=== Test Export/Import Struct and Interface ===');
  
  testExportStruct();
  testExportInterface();
  testExportEnum();
  testImportStructAndInterface();
}

void testExportStruct() {
  print('\n--- Test 1: Export Struct ---');
  
  final code = '''
struct Point {
  x: num;
  y: num;
}

struct Rectangle {
  width: num;
  height: num;
}

export { Point, Rectangle };

fun main(): void {
  print("Struct export test");
}

main();
''';

  runTest(code, 'Export Struct');
}

void testExportInterface() {
  print('\n--- Test 2: Export Interface ---');
  
  final code = '''
interface Drawable {
  fun draw(): void;
}

interface Serializable {
  fun serialize(): string;
}

export { Drawable, Serializable };

fun main(): void {
  print("Interface export test");
}

main();
''';

  runTest(code, 'Export Interface');
}

void testExportEnum() {
  print('\n--- Test 3: Export Enum ---');
  
  final code = '''
enum Color {
  Red,
  Green,
  Blue
}

enum Status {
  Active,
  Inactive
}

export { Color, Status };

fun main(): void {
  print("Enum export test");
}

main();
''';

  runTest(code, 'Export Enum');
}

void testImportStructAndInterface() {
  print('\n--- Test 4: Import Struct and Interface ---');
  
  final code = '''
import "geometry.fl" show Point, Rectangle, Drawable;

fun main(): void {
  print("Import struct and interface test");
}

main();
''';

  runTest(code, 'Import Struct and Interface');
}

void testCompleteStructInterfaceModule() {
  print('\n--- Test 5: Complete Struct/Interface Module ---');
  
  final geometryModule = '''
struct Point {
  x: num;
  y: num;
}

interface Drawable {
  fun draw(): void;
}

Point: Drawable {
  fun new(x: num, y: num): Point {
    return Point { x: x, y: y };
  }
  
  fun draw(): void {
    print("Drawing point at (" + this.x.to_string() + ", " + this.y.to_string() + ")");
  }
}

export { Point, Drawable };
''';

  final mainModule = '''
import "geometry.fl" show Point, Drawable;

fun main(): void {
  let p = Point::new(10, 20);
  p.draw();
}

main();
''';

  print('Geometry module parsing:');
  runTest(geometryModule, 'Geometry Module');
  
  print('Main module parsing:');
  runTest(mainModule, 'Main Module with Struct Import');
}

void runTest(String code, String testName) {
  try {
    final parser = buildParser(verbose: false);
    final ast = parser.parse(code);
    
    if (ast.isFailure) {
      print('❌ $testName - Parse Error: ${ast.message}');
      return;
    }
    
    final compiler = Compiler();
    final bytecode = compiler.compile(ast.value);
    
    final vm = VirtualMachine();
    vm.load(bytecode);
    vm.run();
    
    print('✅ $testName - Success');
  } catch (e) {
    if (e.toString().contains('Module not found') || 
        e.toString().contains('Import operations require async')) {
      print('✅ $testName - Expected Error: ${e.toString().split('\n')[0]}');
    } else {
      print('❌ $testName - Unexpected Error: $e');
    }
  }
}
