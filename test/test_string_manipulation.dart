import 'package:fluent_lang/core/parser.dart';
import 'package:fluent_lang/core/compiler.dart';
import 'package:fluent_lang/core/virtual_machine.dart';

void main() {
  print('=== Test String Manipulation ===');

  testStringLength();
  testStringSubstring();
  testStringIndexOf();
  testStringSplit();
  testStringReplace();
  testStringCase();
  testStringTrim();
  testStringContains();
  testStringStartsEndsWith();
  testStringCharAt();
  testStringRepeat();
  testStringPadding();
  testStringEmpty();
}

void testStringLength() {
  print('\n--- Test 1: String Length ---');

  final code = '''
fun main(): void {
  let text = "Hello World";
  print(text.length());
}

main();
''';

  runTest(code, 'String Length');
}

void testStringSubstring() {
  print('\n--- Test 2: String Substring ---');

  final code = '''
fun main(): void {
  let text = "Hello World";
  print(text.substring(0, 5));
  print(text.substring(6));
}

main();
''';

  runTest(code, 'String Substring');
}

void testStringIndexOf() {
  print('\n--- Test 3: String IndexOf ---');

  final code = '''
fun main(): void {
  let text = "Hello World";
  print(text.index_of("o"));
  print(text.index_of("o", 5));
  print(text.index_of("xyz"));
}

main();
''';

  runTest(code, 'String IndexOf');
}

void testStringSplit() {
  print('\n--- Test 4: String Split ---');

  final code = '''
fun main(): void {
  let text = "apple,banana,cherry";
  let parts = text.split(",");
  print(parts);
}

main();
''';

  runTest(code, 'String Split');
}

void testStringReplace() {
  print('\n--- Test 5: String Replace ---');

  final code = '''
fun main(): void {
  let text = "Hello World";
  print(text.replace("World", "Fluent"));
  print(text.replace("o", "0"));
}

main();
''';

  runTest(code, 'String Replace');
}

void testStringCase() {
  print('\n--- Test 6: String Case ---');

  final code = '''
fun main(): void {
  let text = "Hello World";
  print(text.to_upper_case());
  print(text.to_lower_case());
}

main();
''';

  runTest(code, 'String Case');
}

void testStringTrim() {
  print('\n--- Test 7: String Trim ---');

  final code = '''
fun main(): void {
  let text = "  Hello World  ";
  print(text.trim());
}

main();
''';

  runTest(code, 'String Trim');
}

void testStringContains() {
  print('\n--- Test 8: String Contains ---');

  final code = '''
fun main(): void {
  let text = "Hello World";
  print(text.contains("World"));
  print(text.contains("xyz"));
}

main();
''';

  runTest(code, 'String Contains');
}

void testStringStartsEndsWith() {
  print('\n--- Test 9: String Starts/Ends With ---');

  final code = '''
fun main(): void {
  let text = "Hello World";
  print(text.starts_with("Hello"));
  print(text.ends_with("World"));
  print(text.starts_with("Hi"));
}

main();
''';

  runTest(code, 'String Starts/Ends With');
}

void testStringCharAt() {
  print('\n--- Test 10: String CharAt ---');

  final code = '''
fun main(): void {
  let text = "Hello";
  print(text.char_at(0));
  print(text.char_at(4));
}

main();
''';

  runTest(code, 'String CharAt');
}

void testStringRepeat() {
  print('\n--- Test 11: String Repeat ---');

  final code = '''
fun main(): void {
  let text = "Hi";
  print(text.repeat(3));
}

main();
''';

  runTest(code, 'String Repeat');
}

void testStringPadding() {
  print('\n--- Test 12: String Padding ---');

  final code = '''
fun main(): void {
  let text = "Hi";
  print(text.pad_left(5));
  print(text.pad_right(5, "*"));
}

main();
''';

  runTest(code, 'String Padding');
}

void testStringEmpty() {
  print('\n--- Test 13: String Empty Check ---');

  final code = '''
fun main(): void {
  let empty = "";
  let notEmpty = "Hello";
  print(empty.is_empty());
  print(notEmpty.is_empty());
  print(empty.is_not_empty());
  print(notEmpty.is_not_empty());
}

main();
''';

  runTest(code, 'String Empty Check');
}

void runTest(String code, String testName) {
  try {
    final parser = buildParser(verbose: false);
    final ast = parser.parse(code);

    if (ast.isFailure) {
      print('❌ $testName - Parse Error: ${ast.message}');
      return;
    }

    final compiler = Compiler();
    final bytecode = compiler.compile(ast.value);

    final vm = VirtualMachine();
    vm.load(bytecode);
    vm.run();

    print('✅ $testName - Success');
  } catch (e) {
    print('❌ $testName - Error: $e');
  }
}
