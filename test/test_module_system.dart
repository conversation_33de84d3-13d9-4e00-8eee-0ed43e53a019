import 'package:fluent_lang/core/parser.dart';
import 'package:fluent_lang/core/compiler.dart';
import 'package:fluent_lang/core/virtual_machine.dart';
import 'package:fluent_lang/core/module_system.dart';

void main() {
  print('=== Test Module System ===');

  testImportParsing();
  testExportParsing();
  testModuleCompilation();
  testBasicModuleSystem();
}

void testImportParsing() {
  print('\n--- Test 1: Import Statement Parsing ---');

  final simpleImportCode = '''
import "math.fl";

fun main(): void {
  print("Hello");
}
''';

  final namedImportCode = '''
import { add, subtract } from "math.fl";

fun main(): void {
  print("Hello");
}
''';

  runParseTest(simpleImportCode, 'Simple Import');
  runParseTest(namedImportCode, 'Named Import');
}

void testExportParsing() {
  print('\n--- Test 2: Export Statement Parsing ---');

  final exportCode = '''
fun add(a: num, b: num): num {
  return a + b;
}

fun subtract(a: num, b: num): num {
  return a - b;
}

export { add, subtract };
''';

  runParseTest(exportCode, 'Export Statement');
}

void testModuleCompilation() {
  print('\n--- Test 3: Module Compilation ---');

  final moduleCode = '''
import { helper } from "utils.fl";

fun main(): void {
  print("Main function");
}

export { main };
''';

  runCompileTest(moduleCode, 'Module with Import/Export');
}

void testBasicModuleSystem() {
  print('\n--- Test 4: Basic Module System ---');

  // Create a simple math module
  final mathModuleCode = '''
fun add(a: num, b: num): num {
  return a + b;
}

fun multiply(a: num, b: num): num {
  return a * b;
}

export { add, multiply };
''';

  // Create a main module that imports from math
  final mainModuleCode = '''
import { add } from "math.fl";

fun main(): void {
  let result = add(5, 3);
  print(result);
}

main();
''';

  try {
    // Parse and compile math module
    final parser = buildParser(verbose: false);
    final mathAst = parser.parse(mathModuleCode);

    if (mathAst.isFailure) {
      print('❌ Math Module Parse Error: ${mathAst.message}');
      return;
    }

    final compiler = Compiler();
    final mathBytecode = compiler.compile(mathAst.value);

    // Create module system and register math module
    final moduleSystem = ModuleSystem();
    final mathModule = Module('math.fl', mathAst.value);
    mathModule.bytecode = mathBytecode;
    mathModule.exports.addAll(['add', 'multiply']);
    moduleSystem.registerModule('math.fl', mathModule);

    // Parse and compile main module
    final mainAst = parser.parse(mainModuleCode);

    if (mainAst.isFailure) {
      print('❌ Main Module Parse Error: ${mainAst.message}');
      return;
    }

    final mainBytecode = compiler.compile(mainAst.value);

    // Execute main module with module system
    final vm = VirtualMachine();
    vm.moduleSystem = moduleSystem;
    vm.load(mainBytecode);
    vm.run();

    print('✅ Basic Module System - Success');
  } catch (e) {
    print('❌ Basic Module System - Error: $e');
  }
}

void runParseTest(String code, String testName) {
  try {
    final parser = buildParser(verbose: false);
    final ast = parser.parse(code);

    if (ast.isFailure) {
      print('❌ $testName - Parse Error: ${ast.message}');
      return;
    }

    print('✅ $testName - Success');
  } catch (e) {
    print('❌ $testName - Error: $e');
  }
}

void runCompileTest(String code, String testName) {
  try {
    final parser = buildParser(verbose: false);
    final ast = parser.parse(code);

    if (ast.isFailure) {
      print('❌ $testName - Parse Error: ${ast.message}');
      return;
    }

    final compiler = Compiler();
    final bytecode = compiler.compile(ast.value);

    print('✅ $testName - Success');
  } catch (e) {
    print('❌ $testName - Error: $e');
  }
}
