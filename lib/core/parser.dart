import 'package:fluent_lang/core/ast.dart';
import 'package:fluent_lang/core/type_annotation.dart';
import 'package:petitparser/petitparser.dart';

import 'function_signature.dart';

Parser<Program> buildParser({bool verbose = false}) {
  final tokens = _buildTokens();

  final stmt = undefined();

  // Tạo expression undefined trước để inject sau
  final expression = undefined();

  // Gán expression đúng hàm build
  expression.set(_buildExpression(tokens, verbose: verbose));

  final block = _buildBlock(tokens, stmt);
  final statementParsers = _buildStatements(
    tokens,
    expression,
    stmt,
    block,
    verbose: verbose,
  );
  stmt.set(statementParsers);

  final functionDecl = _buildFunctionDecl(
    tokens,
    stmt,
    block,
    verbose: verbose,
  );
  final structDecl = _buildStructDecl(tokens, verbose: verbose);
  final interfaceDecl = _buildInterfaceDecl(tokens, verbose: verbose);
  final traitImpl = _buildTraitImplementation(
    tokens,
    stmt,
    block,
    verbose: verbose,
  );

  final enumDecl = _buildEnumDecl(tokens, verbose: verbose);
  final declaration = functionDecl | structDecl | interfaceDecl | traitImpl | enumDecl;
  final topLevel = (declaration | stmt);

  final program = topLevel.star().end().map((nodes) {
    return Program(nodes.cast<ASTNode>());
  });
  return program;
}

// ====================== TOKENS ========================
class _Tokens {
  final Parser let,
      fun,
      staticKw,
      returnKw,
      ifKw,
      elseKw,
      whileKw,
      forKw,
      inKw,
      printKw,
      dot;
  final Parser struct, interface, colon, impl, arrow, newKw;
  final Parser enumKw, matchKw, tryKw, catchKw, finallyKw, throwKw;

  final Parser lparen, rparen, lbrace, rbrace, comma, semicolon;
  final Parser lbracket, rbracket;
  final Parser equals, plus, minus, times, divide, less, greater, dotdot;
  final Parser identifier, number, stringLiteral, boolean;

  _Tokens({
    required this.let,
    required this.staticKw,
    required this.fun,
    required this.returnKw,
    required this.ifKw,
    required this.elseKw,
    required this.whileKw,
    required this.forKw,
    required this.inKw,
    required this.printKw,
    required this.newKw,
    required this.struct,
    required this.interface,
    required this.enumKw,
    required this.matchKw,
    required this.tryKw,
    required this.catchKw,
    required this.finallyKw,
    required this.throwKw,
    required this.colon,
    required this.impl,
    required this.arrow,
    required this.lparen,
    required this.rparen,
    required this.lbrace,
    required this.rbrace,
    required this.comma,
    required this.dot,
    required this.semicolon,
    required this.lbracket,
    required this.rbracket,
    required this.equals,
    required this.plus,
    required this.minus,
    required this.times,
    required this.divide,
    required this.less,
    required this.greater,
    required this.dotdot,
    required this.identifier,
    required this.number,
    required this.stringLiteral,
    required this.boolean,
  });
}

_Tokens _buildTokens() {
  trim(Parser p) => p.trim();

  return _Tokens(
    let: trim(string('let')),
    fun: trim(string('fun')),
    staticKw: trim(string('static')),
    dot: trim(char('.')),
    returnKw: trim(string('return')),
    ifKw: trim(string('if')),
    elseKw: trim(string('else')),
    whileKw: trim(string('while')),
    forKw: trim(string('for')),
    inKw: trim(string('in')),
    printKw: trim(string('print')),
    newKw: trim(string('new')),
    struct: trim(string('struct')),
    interface: trim(string('interface')),
    enumKw: trim(string('enum')),
    matchKw: trim(string('match')),
    tryKw: trim(string('try')),
    catchKw: trim(string('catch')),
    finallyKw: trim(string('finally')),
    throwKw: trim(string('throw')),
    colon: trim(char(':')),
    impl: trim(string('impl')),
    arrow: trim(string('->')),
    lparen: trim(char('(')),
    rparen: trim(char(')')),
    lbrace: trim(char('{')),
    rbrace: trim(char('}')),
    comma: trim(char(',')),
    semicolon: trim(char(';')),
    lbracket: trim(char('[')),
    rbracket: trim(char(']')),
    equals: trim(char('=')),
    plus: trim(char('+')),
    minus: trim(char('-')),
    times: trim(char('*')),
    divide: trim(char('/')),
    less: trim(char('<')),
    greater: trim(char('>')),
    dotdot: trim(string('..')),

    boolean: trim(
      string('true').map((_) => BooleanLiteral(true)) |
          string('false').map((_) => BooleanLiteral(false)),
    ),
    identifier: letter()
        .seq(word().star())
        .flatten()
        .trim()
        .map((name) => Identifier(name)),
    number: digit().plus().flatten().trim().map(
      (s) => NumberLiteral(int.parse(s)),
    ),

    stringLiteral: trim(
      char('"')
          .seq(char('"').neg().star().flatten())
          .seq(char('"'))
          .map((v) => StringLiteral(v[1])),
    ),
  );
}

// =================== EXPRESSIONS ======================

Parser withPrecedence(Parser lower, List<Parser<String>> ops) {
  final operator = ops.reduce((a, b) => a.or(b).cast());
  return lower.seq((operator.seq(lower)).star()).map((values) {
    ASTNode expr = values[0];
    for (var pair in values[1]) {
      expr = BinaryExpression(pair[0], expr, pair[1]);
    }
    return expr;
  });
}

Parser _buildExpression(_Tokens t, {bool verbose = false}) {
  final expression = undefined();
  final structliteral = _buildStructLiteral(t, expression);

  // Gọi hàm: foo(1, 2)
  final call = t.identifier
      .seq(t.lparen)
      .seq(expression.separatedBy(t.comma, includeSeparators: false).optional())
      .seq(t.rparen)
      .map((values) {
        if (verbose) print('[CALL]: $values');
        final name = values[0] as Identifier;
        final args = (values[2] as List<dynamic>?) ?? [];
        return CallExpression(name.name, args.cast<ASTNode>());
      });
  // Static call: S::new();
  final staticCall = t.identifier
      .seq(string('::').trim())
      .seq(t.identifier)
      .seq(t.lparen)
      .seq(expression.separatedBy(t.comma, includeSeparators: false).optional())
      .seq(t.rparen)
      .map((v) {
        if (verbose) print('[STATIC CALL]: $v');
        final structName = (v[0] as Identifier).name;
        final methodName = (v[2] as Identifier).name;
        final args = (v[4] as List<dynamic>?) ?? [];
        return StaticMethodCall(structName, methodName, args.cast<ASTNode>());
      });

  // Enum variant access: Color::Red
  final enumVariantAccess = t.identifier
      .seq(string('::').trim())
      .seq(t.identifier)
      .map((v) {
        if (verbose) print('[ENUM VARIANT ACCESS]: $v');
        final enumName = (v[0] as Identifier).name;
        final variant = (v[2] as Identifier).name;
        return EnumVariantAccess(enumName, variant);
      });
  final thisExpr = string('this').trim().map((_) => ThisExpression());

  // Parenthesized expressions
  final parenExpr = t.lparen.seq(expression).seq(t.rparen).map((v) => v[1]);

  final primary =
      structliteral |
      staticCall |
      enumVariantAccess |
      call |
      thisExpr |
      parenExpr |
      t.boolean |
      t.stringLiteral |
      t.number |
      t.identifier;

  final postfix =
      (t.dot
                  .seq(t.identifier)
                  .map((v) => {'kind': 'field', 'name': (v[1] as Identifier).name}) |
              t.lparen
                  .seq(
                    expression
                        .separatedBy(t.comma, includeSeparators: false)
                        .optional(),
                  )
                  .seq(t.rparen)
                  .map((v) => {'kind': 'call', 'args': v[1] ?? []}) |
              t.lbracket
                  .seq(expression)
                  .seq(t.rbracket)
                  .map((v) => {'kind': 'index', 'index': v[1]}))
          .star();

  final chained = primary.seq(postfix).map((v) {
    ASTNode result = v[0];
    final postfixOps = v[1] as List<dynamic>;

    for (int i = 0; i < postfixOps.length; i++) {
      var p = postfixOps[i];

      if (p['kind'] == 'field') {
        // Check if next operation is a call - if so, combine into method call
        if (i + 1 < postfixOps.length && postfixOps[i + 1]['kind'] == 'call') {
          final methodName = p['name'] as String;
          final nextOp = postfixOps[i + 1];
          final args = (nextOp['args'] as List<dynamic>).cast<ASTNode>();
          result = MethodCall(result, methodName, args);
          i++; // Skip the next call operation since we consumed it
        } else {
          result = FieldAccess(result, p['name']);
        }
      } else if (p['kind'] == 'call') {
        final args = (p['args'] as List<dynamic>).cast<ASTNode>();
        if (result is Identifier) {
          result = CallExpression(result.name, args);
        } else {
          result = MethodCall(result, 'call', args);
        }
      } else if (p['kind'] == 'index') {
        final index = p['index'] as ASTNode;
        result = ArrayIndexing(result, index);
      }
    }
    return result;
  });

  final unary = undefined();
  unary.set((
    char('!').trim().seq(unary).map((v) {
      if (verbose) print('[UNARY NOT]: $v');
      return UnaryExpression('!', v[1]);
    }) |
    char('-').trim().seq(unary).map((v) {
      if (verbose) print('[UNARY NEG]: $v');
      return UnaryExpression('-', v[1]);
    }) |
    char('+').trim().seq(unary).map((v) {
      if (verbose) print('[UNARY POS]: $v');
      return UnaryExpression('+', v[1]);
    })
  ).or(chained));

  // Các cấp độ ưu tiên (từ thấp đến cao)
  final mulOps = <Parser<String>>[
    t.times.cast<String>(),
    t.divide.cast<String>(),
    char('%').trim().cast<String>(),
  ];
  final addOps = <Parser<String>>[
    t.plus.cast<String>(),
    t.minus.cast<String>(),
  ];
  final relOps = <Parser<String>>[
    string('>=').trim(),
    string('<=').trim(),
    t.less.cast<String>(),
    t.greater.cast<String>(),
  ];
  final eqOps = <Parser<String>>[string('==').trim(), string('!=').trim()];

  final mul = withPrecedence(unary, mulOps);
  final add = withPrecedence(mul, addOps);
  final rel = withPrecedence(add, relOps);
  final eq = withPrecedence(rel, eqOps);

  expression.set(eq); // eq là cấp cao nhất

  return expression;
}

// ======================= BLOCK ========================
Parser _buildBlock(_Tokens t, Parser stmt) {
  // Comment parser để skip
  final comment = (string('//') | string('///'))
      .seq(any().starLazy(newline() | endOfInput()).flatten())
      .seq((newline() | endOfInput()))
      .map((v) => null); // Return null để filter out

  final stmtOrComment = (comment | stmt);

  return t.lbrace
      .seq(stmtOrComment.star())
      .seq(t.rbrace)
      .map((v) {
        final items = v[1] as List<dynamic>;
        // Filter out null values (comments)
        return items.where((item) => item != null).cast<ASTNode>().toList();
      });
}

// =================== STATEMENTS =======================
Parser _buildStatements(
  _Tokens t,
  Parser expression,
  Parser stmt,
  Parser block, {
  bool verbose = false,
}) {
  Parser letDecl() => t.let
      .seq(t.identifier.separatedBy(t.comma, includeSeparators: false))
      .seq(t.equals)
      .seq(expression.separatedBy(t.comma, includeSeparators: false))
      .seq(t.semicolon)
      .map((v) {
        if (verbose) print('[LET DECL]: $v');
        final names = (v[1] as List<dynamic>).cast<Identifier>().map((id) => id.name).toList();
        final values = (v[3] as List<dynamic>).cast<ASTNode>();
        return VariableDeclaration(names, values);
      });

  Parser returnStmt() => t.returnKw.seq(expression).seq(t.semicolon).map((v) {
    if (verbose) print('[RETURN STMT]: $v');
    return ReturnStatement(v[1]);
  });

  Parser printStmt() => t.printKw
      .seq(t.lparen)
      .seq(expression)
      .seq(t.rparen)
      .seq(t.semicolon)
      .map((v) {
        if (verbose) print('[PRINT STMT]: $v');
        return PrintStatement(v[2]);
      });

  Parser ifStmt() => t.ifKw
      .seq(expression)
      .seq(block)
      .seq((t.elseKw.seq(block)).optional())
      .map((v) {
        if (verbose) print('[IF STMT]: $v');
        final condition = v[1] as ASTNode;
        final thenBranch = (v[2] as List<dynamic>).cast<ASTNode>();
        final elseBranch = v[3] != null ? (v[3][1] as List<dynamic>).cast<ASTNode>() : null;
        return IfStatement(condition, thenBranch, elseBranch);
      });

  Parser assignStmt() {
    final operator = (char('=') | string('+=') | string('-=')).trim();

    return expression.seq(operator).seq(expression).seq(t.semicolon).map((v) {
      if (verbose) print('[ASSIGN STMT]: $v');
      final target = v[0] as ASTNode;
      final op = v[1] as String;
      final value = v[2] as ASTNode;

      if (op == '=') {
        return AssignmentExpression(target: target, value: value);
      } else {
        // e.g., i += 1  => i = i + 1
        final binOp = BinaryExpression(op[0], target, value); // '+' or '-'
        return AssignmentExpression(target: target, value: binOp);
      }
    });
  }

  Parser whileStmt() => t.whileKw.seq(expression).seq(block).map((v) {
    if (verbose) print('[WHILE STMT]: $v');
    final condition = v[1] as ASTNode;
    final body = (v[2] as List<dynamic>).cast<ASTNode>();
    return WhileStatement(condition, body);
  });

  Parser forStmt() => t.forKw
      .seq(t.identifier)
      .seq(t.inKw)
      .seq(expression)
      .seq(t.dotdot)
      .seq(expression)
      .seq(block)
      .map((v) {
        if (verbose) print('[FOR STMT]: $v');
        final iterator = (v[1] as Identifier).name;
        final start = v[3] as ASTNode;
        final end = v[5] as ASTNode;
        final body = (v[6] as List<dynamic>).cast<ASTNode>();
        return ForStatement(iterator, start, end, body);
      });

  Parser exprStmt() => expression.seq(t.semicolon).map((v) {
    if (verbose) print('[EXPR STMT]: $v');
    return ExpressionStatement(v[0]);
  });
  Parser matchStmt() => _buildMatchExpression(t, expression).map((v) {
    if (verbose) print('[MATCH STMT]: $v');
    return ExpressionStatement(v);
  });

  Parser tryStmt() => _buildTryStatement(t, stmt, block);

  Parser throwStmt() => t.throwKw.seq(expression).seq(t.semicolon).map((v) {
    if (verbose) print('[THROW STMT]: $v');
    return ThrowStatement(v[1]);
  });

  return letDecl() |
      assignStmt() |
      returnStmt() |
      printStmt() |
      ifStmt() |
      whileStmt() |
      forStmt() |
      matchStmt() |
      tryStmt() |
      throwStmt() |
      exprStmt();
}

// =================== FUNCTION ========================
Parser _buildFunctionDecl(
  _Tokens t,
  Parser stmt,
  Parser block, {
  bool verbose = false,
}) {
  final typedParam = _buildTypedParam(t);
  final typeParser = _buildType(t);

  return t.staticKw
      .optional()
      .seq(t.fun)
      .seq(
        t.identifier |
            t.newKw.map((_) => Identifier('new')),
      )
      .seq(t.lparen)
      .seq(typedParam.separatedBy(t.comma, includeSeparators: false).optional())
      .seq(t.rparen)
      .seq(
        char(':').trim().seq(typeParser).map((v) => v[1]).optional(),
      ) // returnType
      .seq(block)
      .map((v) {
        if (verbose) print('[FUNCTION DECL]: $v');
        final isStatic = v[0] != null;
        final name = v[2] as Identifier;
        final params = (v[4] as List<dynamic>?) ?? [];
        final returnType = v[6] as TypeAnnotation?;
        final body = (v[7] as List<dynamic>).cast<ASTNode>();
        return FunctionDeclaration(
          name,
          params.cast<Parameter>(),
          body,
          isStatic: isStatic,
          returnType: returnType,
        );
      });
}

Parser _buildType(_Tokens t, {bool verbose = false}) {
  final type = undefined(); // type gốc (full recursive)

  final identifier = t.identifier.map((id) {
    if (verbose) print('[TYPE]: $id');
    final name = (id as Identifier).name;
    return NamedType(name);
  });

  final traitBound = t.identifier
      .seq(char(':').trim())
      .seq(
        t.identifier
            .separatedBy(char('&').trim(), includeSeparators: false)
            .map(
              (traits) =>
                  traits
                      .map((e) => (e as Identifier).name)
                      .toList(),
            ),
      )
      .map((v) {
        if (verbose) print('[TRAIT BOUND]: $v');
        final name = (v[0] as Identifier).name;
        final traits = v[2] as List<String>;
        return TraitBoundType(name, traits);
      });

  final generic = t.identifier
      .seq(char('<').trim())
      .seq((traitBound | type).separatedBy(t.comma, includeSeparators: false))
      .seq(char('>').trim())
      .map((v) {
        if (verbose) print('[GENERIC]: $v');
        final base = (v[0] as Identifier).name;
        final args = (v[2] as List<dynamic>).cast<TypeAnnotation>();
        return GenericType(base, args);
      });

  // base type KHÔNG có đệ quy trong chính nó
  final primary = generic | traitBound | identifier;

  // postfix: ?, ví dụ `string?` hoặc `Box<num>?`
  final optional = primary.seq(char('?').trim()).map((v) {
    if (verbose) print('[OPTIONAL]: $v');
    final base = v[0] as TypeAnnotation;
    return OptionalType(base);
  });

  // Chỉ khi nào primary không có `?`, thì giữ nguyên
  type.set(optional | primary);

  return type;
}

Parser _buildTypedParam(_Tokens t, {bool verbose = false}) {
  final typeParser = _buildType(t);
  return t.identifier.seq(t.colon).seq(typeParser).map((v) {
    if (verbose) print("BUILD Typed Param: $v");
    final name = v[0] as Identifier;
    final type = v[2] as TypeAnnotation;
    return Parameter(name, type);
  });
}

Parser _buildStructDecl(_Tokens t, {bool verbose = false}) {
  final typeParser = _buildType(t);
  final field = t.identifier.seq(t.colon).seq(typeParser);
  // Bổ sung dấu ; hoặc , hoặc không có gì
  final fields = field.seq((t.semicolon | t.comma).optional()).star();
  // struct Box<T> {...}
  final genericParam = t.identifier
      .seq((t.colon.seq(_buildType(t))).optional())
      .map(
        (v) => {
          'name': (v[0] as Identifier).name,
          if (v[1] != null) 'bound': v[1][1], // [colon, boundType]
        },
      );

  final genericParams = char('<')
      .trim()
      .seq(genericParam.separatedBy(t.comma, includeSeparators: false))
      .seq(char('>').trim())
      .map((v) => v[1]);

  return t.struct
      .seq(t.identifier)
      .seq(genericParams.optional())
      .seq(t.lbrace)
      .seq(fields.optional())
      .seq(t.rbrace)
      .map((v) {
        if (verbose) print("STRUCT DECL: $v");
        final name = (v[1] as Identifier).name;
        final fieldsList = (v[4] as List<dynamic>?) ?? [];
        final fields = Map<String, TypeAnnotation>.fromEntries(
          fieldsList.map((f) {
            // f is [identifier, colon, typeAnnotation, separator]

            final fieldName = (f[0] as Identifier).name;
            final fieldType = f[2] as TypeAnnotation;
            return MapEntry(fieldName, fieldType);
          }),
        );
        return StructDeclaration(name, fields);
      });
}

Parser _buildInterfaceDecl(_Tokens t, {bool verbose = false}) {
  final traitBound = t.identifier
      .seq(char('&').trim().seq(t.identifier).map((v) => v[1]).star())
      .map((v) {
        final traits =
            [
              v[0],
              ...v[1],
            ].map((e) => (e as Identifier).name).toList();
        return traits;
      });

  final genericParam = t.identifier
      .seq(
        t.colon
            .trim()
            .seq(traitBound)
            .map((v) => v[1]) // danh sách traits
            .optional(),
      )
      .map((v) {
        final name = (v[0] as Identifier).name;
        final traits = v[1];
        if (verbose) print('[GENERIC PARAM]: name=$name, traits=$traits');

        return {
          'name': name,
          if (traits != null)
            'bound': {'kind': 'trait_bound', 'name': name, 'traits': traits},
        };
      });

  final genericParams = char('<')
      .trim()
      .seq(genericParam.separatedBy(t.comma, includeSeparators: false))
      .seq(char('>').trim())
      .map((v) => v[1]);

  final methodSig = t.fun
      .seq(t.identifier)
      .seq(t.lparen)
      .seq(
        _buildTypedParam(
          t,
        ).separatedBy(t.comma, includeSeparators: false).optional(),
      )
      .seq(t.rparen)
      .seq(t.colon)
      .seq(_buildType(t))
      .seq(t.semicolon)
      .map((v) {
        if (verbose) print("METHOD SIG: $v");
        final name = (v[1] as Identifier).name;
        final params = (v[3] as List<dynamic>?) ?? [];
        final returnType = v[6] as TypeAnnotation;
        return FunctionSignature(
          name: name,
          paramTypes: params.cast<Parameter>(),
          returnType: returnType,
        );
      });

  return t.interface
      .seq(t.identifier)
      .seq(genericParams.optional())
      .seq(t.lbrace)
      .seq(methodSig.star())
      .seq(t.rbrace)
      .map((v) {
        if (verbose) print("INTERFACE DECL: $v");
        final name = (v[1] as Identifier).name;
        final methods = (v[4] as List<dynamic>).cast<FunctionSignature>();
        return InterfaceDeclaration(name, methods);
      });
}

Parser _buildTraitImplementation(
  _Tokens t,
  Parser stmt,
  Parser block, {
  bool verbose = false,
}) {
  final methods = _buildFunctionDecl(t, stmt, block);
  final traitType = _buildType(t); // đã hỗ trợ generic, trait bound, optional
  final structType = _buildType(t); // Support generic struct names like Array<T>

  return structType
      .seq(t.colon)
      .seq(traitType)
      .seq(t.lbrace)
      .seq(methods.star())
      .seq(t.rbrace)
      .map((v) {
        if (verbose) print("TRAIT IMPL: $v");
        final structName = v[0] as TypeAnnotation; // Keep as TypeAnnotation
        final traitName = v[2] as TypeAnnotation; // Keep as TypeAnnotation
        final methods = (v[4] as List<dynamic>).cast<FunctionDeclaration>();
        return TraitImplementation(structName, traitName, methods);
      });
}

Parser _buildEnumDecl(_Tokens t, {bool verbose = false}) {
  final typeParser = _buildType(t);

  // Enum variant: Name or Name(Type1, Type2, ...)
  final enumVariant = t.identifier
      .seq(
        t.lparen
            .seq(typeParser.separatedBy(t.comma, includeSeparators: false))
            .seq(t.rparen)
            .map((v) => v[1])
            .optional()
      )
      .map((v) {
        final name = (v[0] as Identifier).name;
        final fields = v[1] as List<TypeAnnotation>?;
        return EnumVariant(name, fields: fields);
      });

  return t.enumKw
      .seq(t.identifier)
      .seq(t.lbrace)
      .seq(enumVariant.separatedBy(t.comma, includeSeparators: false))
      .seq(t.rbrace)
      .map((v) {
        if (verbose) print("ENUM DECL: $v");
        final name = (v[1] as Identifier).name;
        final variants = (v[3] as List<dynamic>).cast<EnumVariant>();
        return EnumDeclaration(name, variants);
      });
}

Parser _buildMatchExpression(_Tokens t, Parser expression) {
  // Pattern parsers
  final literalPattern = (t.number | t.stringLiteral | t.boolean)
      .map((v) => LiteralPattern(v));

  final identifierPattern = t.identifier
      .map((v) => IdentifierPattern((v as Identifier).name));

  final wildcardPattern = char('_').trim()
      .map((v) => WildcardPattern());

  final pattern = undefined();

  final enumPattern = t.identifier
      .seq(string('::').trim())
      .seq(t.identifier)
      .seq(
        t.lparen
            .seq(pattern.separatedBy(t.comma, includeSeparators: false))
            .seq(t.rparen)
            .map((v) => v[1])
            .optional()
      )
      .map((v) {
        final enumName = (v[0] as Identifier).name;
        final variant = (v[2] as Identifier).name;
        final subPatterns = v[3] as List<Pattern>?;
        return EnumPattern(enumName, variant, subPatterns: subPatterns);
      });

  pattern.set(enumPattern | literalPattern | identifierPattern | wildcardPattern);

  // Match arm: pattern => expression
  final matchArm = pattern
      .seq(string('=>').trim())
      .seq(expression)
      .map((v) => MatchArm(v[0], v[2]));

  return t.matchKw
      .seq(expression)
      .seq(t.lbrace)
      .seq(matchArm.separatedBy(t.comma, includeSeparators: false))
      .seq(t.rbrace)
      .map((v) => MatchExpression(v[1], v[3].cast<MatchArm>()));
}

Parser _buildTryStatement(_Tokens t, Parser stmt, Parser block) {
  final catchClause = t.catchKw
      .seq(
        t.lparen
            .seq(t.identifier) // exception type
            .seq(t.identifier.optional()) // exception name
            .seq(t.rparen)
            .optional()
      )
      .seq(block)
      .map((v) {
        final params = v[1];
        final body = (v[2] as List<dynamic>).cast<ASTNode>();

        if (params != null) {
          final exceptionType = (params[1] as Identifier).name;
          final exceptionName = params[2] != null ? (params[2] as Identifier).name : null;
          return CatchClause(body, exceptionType: exceptionType, exceptionName: exceptionName);
        } else {
          return CatchClause(body);
        }
      });

  final finallyClause = t.finallyKw
      .seq(block)
      .map((v) => (v[1] as List<dynamic>).cast<ASTNode>());

  return t.tryKw
      .seq(block)
      .seq(catchClause.star())
      .seq(finallyClause.optional())
      .map((v) {
        final tryBlock = (v[1] as List<dynamic>).cast<ASTNode>();
        final catchClauses = (v[2] as List<dynamic>).cast<CatchClause>();
        final finallyBlock = v[3] as List<ASTNode>?;
        return TryStatement(tryBlock, catchClauses, finallyBlock: finallyBlock);
      });
}

Parser _buildStructLiteral(_Tokens t, Parser expr) {
  final pair = t.identifier.seq(t.colon).seq(expr);
  final pairs = pair.separatedBy(t.comma, includeSeparators: false).optional();

  return t.identifier
      .seq(t.lbrace)
      .seq(pairs)
      .seq(t.rbrace)
      .map(
        (v) {
          final name = (v[0] as Identifier).name;
          final fieldsList = (v[2] as List<dynamic>?) ?? [];
          final fields = Map<String, ASTNode>.fromEntries(
            fieldsList.map((f) {
              final fieldName = (f[0] as Identifier).name;
              final fieldValue = f[2] as ASTNode;
              return MapEntry(fieldName, fieldValue);
            }),
          );
          return StructLiteral(name, fields);
        },
      );
}

// Helper function to parse type annotations from Map format
TypeAnnotation parseTypeAnnotation(dynamic node) {
  if (node is String) {
    return NamedType(node);
  }
  if (node is Map<String, dynamic>) {
    switch (node['kind']) {
      case 'named':
        return NamedType(node['name']);

      case 'generic':
        return GenericType(
          node['base'],
          (node['args'] as List).map(parseTypeAnnotation).toList(),
        );

      case 'union':
        return UnionType(
          (node['types'] as List).map(parseTypeAnnotation).toList(),
        );

      case 'optional':
        return OptionalType(parseTypeAnnotation(node['base']));

      case 'trait_bound':
        return TraitBoundType(node['name'], List<String>.from(node['bounds']));
      case 'struct':
        return StructType(
          node['name'],
          Map.fromEntries(
            (node['fields'] as Map<String, dynamic>).entries.map(
              (e) => MapEntry(e.key, parseTypeAnnotation(e.value)),
            ),
          ),
        );
      case 'function':
        return FunctionType(
          (node['params'] as List).map(parseTypeAnnotation).toList(),
          parseTypeAnnotation(node['return']),
        );
    }
  }
  throw Exception('Invalid type annotation: $node');
}
