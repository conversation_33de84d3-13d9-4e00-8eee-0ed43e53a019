import 'dart:io';
import 'parser.dart';
import 'compiler.dart';
import 'virtual_machine.dart';
import 'bytecode.dart';
import 'ast.dart';

class ModuleSystem {
  final Map<String, Module> _loadedModules = {};
  final Map<String, String> _moduleCache = {}; // path -> source code
  final Set<String> _loadingModules = {}; // for circular dependency detection
  bool _verbose = false;

  set verbose(bool value) => _verbose = value;

  Module? getModule(String path) {
    return _loadedModules[path];
  }

  bool isModuleLoaded(String path) {
    return _loadedModules.containsKey(path);
  }

  void registerModule(String path, Module module) {
    _loadedModules[path] = module;
  }

  Future<Module> loadModule(String path, {String? basePath}) async {
    final resolvedPath = _resolvePath(path, basePath);

    if (_loadedModules.containsKey(resolvedPath)) {
      return _loadedModules[resolvedPath]!;
    }

    if (_verbose) {
      print('[MODULE SYSTEM]: Loading module from $resolvedPath');
    }

    // Check for circular dependencies
    if (_isCircularDependency(resolvedPath)) {
      throw Exception('Circular dependency detected: $resolvedPath');
    }

    // Mark as loading
    _loadingModules.add(resolvedPath);

    try {
      // Read source code
      final sourceCode = await _readModuleSource(resolvedPath);

    // Parse module
    final parser = buildParser(verbose: _verbose);
    final parseResult = parser.parse(sourceCode);

    if (parseResult.isFailure) {
      throw Exception('Failed to parse module $resolvedPath: ${parseResult.message}');
    }

    final ast = parseResult.value;

    // Extract exports and imports
    final module = Module(resolvedPath, ast);
    _extractModuleInfo(module, ast);

    // Compile module
    final compiler = Compiler();
    compiler.verbose = _verbose;
    final bytecode = compiler.compile(ast);
    module.bytecode = bytecode;

      // Cache the module
      _loadedModules[resolvedPath] = module;

      if (_verbose) {
        print('[MODULE SYSTEM]: Module $resolvedPath loaded successfully');
        print('[MODULE SYSTEM]: Exports: ${module.exports}');
        print('[MODULE SYSTEM]: Imports: ${module.imports.keys.toList()}');
      }

      return module;
    } finally {
      // Remove from loading set
      _loadingModules.remove(resolvedPath);
    }
  }

  bool _isCircularDependency(String path) {
    return _loadingModules.contains(path);
  }

  String _resolvePath(String path, String? basePath) {
    // Handle Dart-style package imports (for future use)
    if (path.startsWith('package:')) {
      // For now, treat as relative path without package: prefix
      path = path.substring(8);
    }

    // Add .fl extension if not present
    if (!path.endsWith('.fl')) {
      path = '$path.fl';
    }

    if (path.startsWith('/') || path.startsWith('C:') || path.startsWith('\\')) {
      // Absolute path
      return path;
    }

    if (basePath != null) {
      // Relative to base path
      final baseDir = File(basePath).parent.path;
      return '$baseDir/$path';
    }

    // Relative to current directory
    return path;
  }

  Future<String> _readModuleSource(String path) async {
    if (_moduleCache.containsKey(path)) {
      return _moduleCache[path]!;
    }

    final file = File(path);
    if (!await file.exists()) {
      throw Exception('Module file not found: $path');
    }

    final source = await file.readAsString();
    _moduleCache[path] = source;
    return source;
  }

  void _extractModuleInfo(Module module, Program ast) {
    for (final node in ast.body) {
      if (node is ImportStatement) {
        // Convert ImportSpecifier list to string list
        final importNames = node.imports?.map((spec) => spec.name).toList();
        module.imports[node.path] = importNames;
      } else if (node is ExportStatement) {
        module.exports.addAll(node.exports);
      } else if (node is FunctionDeclaration) {
        // Functions are potentially exportable
        module.declarations[node.name.name] = node;
      } else if (node is StructDeclaration) {
        // Structs are potentially exportable
        module.declarations[node.name] = node;
      } else if (node is InterfaceDeclaration) {
        // Interfaces are potentially exportable
        module.declarations[node.name] = node;
      } else if (node is EnumDeclaration) {
        // Enums are potentially exportable
        module.declarations[node.name] = node;
      }
    }
  }

  Future<void> executeModule(String path, VirtualMachine vm, {String? basePath}) async {
    final module = await loadModule(path, basePath: basePath);

    if (module.bytecode == null) {
      throw Exception('Module $path has no bytecode');
    }

    // Load module dependencies first
    for (final importPath in module.imports.keys) {
      await _loadModuleDependency(importPath, module, vm, basePath: path);
    }

    // Execute the module
    vm.load(module.bytecode!);
    vm.run();
  }

  Future<void> _loadModuleDependency(String importPath, Module parentModule, VirtualMachine vm, {String? basePath}) async {
    final dependencyModule = await loadModule(importPath, basePath: basePath);

    if (dependencyModule.bytecode == null) {
      throw Exception('Dependency module $importPath has no bytecode');
    }

    // Execute dependency module to populate its exports
    final dependencyVm = VirtualMachine();
    dependencyVm.verbose = _verbose;
    dependencyVm.load(dependencyModule.bytecode!);
    dependencyVm.run();

    // Import specific items or all exports
    final importList = parentModule.imports[importPath];
    if (importList == null) {
      // Import all exports
      for (final exportName in dependencyModule.exports) {
        if (dependencyVm.vars.containsKey(exportName)) {
          vm.vars[exportName] = dependencyVm.vars[exportName];
        }
      }
    } else {
      // Import specific items
      for (final importName in importList) {
        if (dependencyVm.vars.containsKey(importName)) {
          vm.vars[importName] = dependencyVm.vars[importName];
        } else {
          throw Exception('Export "$importName" not found in module $importPath');
        }
      }
    }
  }
}

class Module {
  final String path;
  final Program ast;
  Bytecode? bytecode;

  final Set<String> exports = {};
  final Map<String, List<String>?> imports = {}; // path -> import list (null = import all)
  final Map<String, ASTNode> declarations = {}; // name -> declaration

  Module(this.path, this.ast);

  @override
  String toString() => 'Module($path, exports: $exports, imports: ${imports.keys.toList()})';
}
