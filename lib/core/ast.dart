import 'type_annotation.dart';
import 'function_signature.dart';

abstract class ASTNode {}

class Program extends ASTNode {
  final List<ASTNode> body;

  Program(this.body);

  @override
  String toString() => body.join('\n');
}

class NumberLiteral extends ASTNode {
  final int value;

  NumberLiteral(this.value);

  @override
  String toString() => '$value';
}

class StringLiteral extends ASTNode {
  final String value;

  StringLiteral(this.value);

  @override
  String toString() => '"$value"';
}

class BooleanLiteral extends ASTNode {
  final bool value;

  BooleanLiteral(this.value);

  @override
  String toString() => value ? 'true' : 'false';
}

class NullLiteral extends ASTNode {}

class Identifier extends ASTNode {
  final String name;

  Identifier(this.name);

  @override
  String toString() => name;

  factory Identifier.from(Map<String, dynamic> json) {
    return Identifier(json['name'] as String);
  }
}

class BinaryExpression extends ASTNode {
  final String operator;
  final ASTNode left;
  final ASTNode right;

  BinaryExpression(this.operator, this.left, this.right);

  @override
  String toString() => '$left $operator $right';
}

class VariableDeclaration extends ASTNode {
  final List<String> names;
  final List<ASTNode> values;

  VariableDeclaration(this.names, this.values);

  @override
  String toString() => 'let $names = $values;';
}

class FunctionDeclaration extends ASTNode {
  final Identifier name;
  final List<Parameter> params;

  final TypeAnnotation? returnType;
  final List<ASTNode> body;
  final bool isStatic;

  FunctionDeclaration(
    this.name,
    this.params,
    this.body, {
    this.isStatic = false,
    this.returnType,
  });

  @override
  String toString() => 'fun $name($params) {${body.join('\n')}}';
}

class ConstructorDeclaration extends ASTNode {
  final String structName;
  final List<String> params;
  final List<ASTNode> body;

  ConstructorDeclaration(this.structName, this.params, this.body);

  @override
  String toString() => 'fun new($params) {${body.join('\n')}}';
}

class FieldAccess extends ASTNode {
  final ASTNode object;
  final String field;

  FieldAccess(this.object, this.field);

  @override
  String toString() => '$object.$field';
}

class ArrayIndexing extends ASTNode {
  final ASTNode array;
  final ASTNode index;

  ArrayIndexing(this.array, this.index);

  @override
  String toString() => '$array[$index]';
}

class CallExpression extends ASTNode {
  final String name;
  final List<ASTNode> arguments;

  CallExpression(this.name, this.arguments);

  @override
  String toString() => '$name($arguments)';
}

class ReturnStatement extends ASTNode {
  final ASTNode value;

  ReturnStatement(this.value);

  @override
  String toString() => 'return $value;';
}

class PrintStatement extends ASTNode {
  final ASTNode value;

  PrintStatement(this.value);
}

class IfStatement extends ASTNode {
  final ASTNode condition;
  final List<ASTNode> thenBranch;
  final List<ASTNode>? elseBranch;

  IfStatement(this.condition, this.thenBranch, [this.elseBranch]);

  @override
  String toString() =>
      'if $condition {${thenBranch.join('\n')}}${elseBranch != null ? ' else {${elseBranch!.join('\n')}}' : ''}';
}

class WhileStatement extends ASTNode {
  final ASTNode condition;
  final List<ASTNode> body;

  WhileStatement(this.condition, this.body);

  @override
  String toString() => 'while $condition {${body.join('\n')}}';
}

class ForStatement extends ASTNode {
  final String iterator;
  final ASTNode start;
  final ASTNode end;
  final List<ASTNode> body;

  ForStatement(this.iterator, this.start, this.end, this.body);

  @override
  String toString() => 'for $iterator in $start..$end {${body.join('\n')}}';
}

class ExpressionStatement extends ASTNode {
  final ASTNode expression;

  ExpressionStatement(this.expression);

  @override
  String toString() => '$expression;';
}

class AssignmentExpression extends ASTNode {
  final ASTNode target;
  final ASTNode value;

  AssignmentExpression({required this.target, required this.value})
    : assert(
        target is Identifier || target is FieldAccess,
        'Invalid assignment target: $target',
      );

  @override
  String toString() => '$target = $value';
}

class StructDeclaration extends ASTNode {
  final String name;
  final Map<String, TypeAnnotation> fields;

  StructDeclaration(this.name, this.fields);

  @override
  String toString() => 'struct $name {${fields.entries.join(', ')}}';
}

class InterfaceDeclaration extends ASTNode {
  final String name;
  final List<FunctionSignature> methods;

  InterfaceDeclaration(this.name, this.methods);

  @override
  String toString() => 'interface $name {${methods.join('\n')}}';
}

class TraitImplementation extends ASTNode {
  final dynamic structName; // Can be String or TypeAnnotation
  final dynamic traitName; // Can be String or TypeAnnotation
  final List<FunctionDeclaration> methods; // + optional constructor(s)
  TraitImplementation(this.structName, this.traitName, this.methods);

  @override
  String toString() => '$structName: $traitName {${methods.join('\n')}}';
}

class EnumDeclaration extends ASTNode {
  final String name;
  final List<EnumVariant> variants;

  EnumDeclaration(this.name, this.variants);

  @override
  String toString() => 'enum $name { ${variants.join(', ')} }';
}

class EnumVariant extends ASTNode {
  final String name;
  final List<TypeAnnotation>? fields; // For tuple variants: Some(T)

  EnumVariant(this.name, {this.fields});

  @override
  String toString() => fields != null ? '$name(${fields!.join(', ')})' : name;
}

class MatchExpression extends ASTNode {
  final ASTNode expression;
  final List<MatchArm> arms;

  MatchExpression(this.expression, this.arms);

  @override
  String toString() => 'match $expression { ${arms.join(', ')} }';
}

class MatchArm extends ASTNode {
  final Pattern pattern;
  final ASTNode? guard; // Optional when clause
  final ASTNode body;

  MatchArm(this.pattern, this.body, {this.guard});

  @override
  String toString() => '$pattern => $body';
}

abstract class Pattern extends ASTNode {}

class LiteralPattern extends Pattern {
  final ASTNode literal;

  LiteralPattern(this.literal);

  @override
  String toString() => literal.toString();
}

class IdentifierPattern extends Pattern {
  final String name;

  IdentifierPattern(this.name);

  @override
  String toString() => name;
}

class EnumPattern extends Pattern {
  final String enumName;
  final String variant;
  final List<Pattern>? subPatterns;

  EnumPattern(this.enumName, this.variant, {this.subPatterns});

  @override
  String toString() => subPatterns != null
      ? '$enumName::$variant(${subPatterns!.join(', ')})'
      : '$enumName::$variant';
}

class WildcardPattern extends Pattern {
  @override
  String toString() => '_';
}

class TryStatement extends ASTNode {
  final List<ASTNode> tryBlock;
  final List<CatchClause> catchClauses;
  final List<ASTNode>? finallyBlock;

  TryStatement(this.tryBlock, this.catchClauses, {this.finallyBlock});

  @override
  String toString() => 'try { ${tryBlock.join('; ')} } ${catchClauses.join(' ')}';
}

class CatchClause extends ASTNode {
  final String? exceptionType;
  final String? exceptionName;
  final List<ASTNode> body;

  CatchClause(this.body, {this.exceptionType, this.exceptionName});

  @override
  String toString() => 'catch${exceptionType != null ? ' ($exceptionType${exceptionName != null ? ' $exceptionName' : ''})' : ''} { ${body.join('; ')} }';
}

class ThrowStatement extends ASTNode {
  final ASTNode expression;

  ThrowStatement(this.expression);

  @override
  String toString() => 'throw $expression';
}

class StructLiteral extends ASTNode {
  final String name;
  final Map<String, ASTNode> fields;

  StructLiteral(this.name, this.fields);

  @override
  String toString() => '$name{${fields.entries.join(', ')}}';
}

class Block extends ASTNode {
  final List<ASTNode> statements;

  Block(this.statements);

  @override
  String toString() => '{${statements.join('\n')}}';
}

class FieldAssign extends ASTNode {
  final ASTNode object;
  final String field;
  final ASTNode value;

  FieldAssign(this.object, this.field, this.value);

  @override
  String toString() => '$object.$field = $value';
}

class MethodCall extends ASTNode {
  final ASTNode receiver; // instance expression
  final String method;
  final List<ASTNode> arguments;

  MethodCall(this.receiver, this.method, this.arguments);

  @override
  String toString() => '$receiver.$method($arguments)';
}

class StaticMethodCall extends ASTNode {
  final String structName;
  final String method;
  final List<ASTNode> arguments;

  StaticMethodCall(this.structName, this.method, this.arguments);

  @override
  String toString() => '$structName::$method($arguments)';
}

class ThisExpression extends ASTNode {
  ThisExpression();

  @override
  String toString() => 'this';
}

class UnaryExpression extends ASTNode {
  final String operator;
  final ASTNode operand;

  UnaryExpression(this.operator, this.operand);

  @override
  String toString() => 'unary$operator$operand';
}

class EnumVariantAccess extends ASTNode {
  final String enumName;
  final String variant;

  EnumVariantAccess(this.enumName, this.variant);

  @override
  String toString() => '$enumName::$variant';
}

class Comment extends ASTNode {
  final String text;

  Comment(this.text);

  @override
  String toString() => '// $text';
}

class ImportStatement extends ASTNode {
  final String path;
  final String? prefix; // import prefix (as alias)
  final List<ImportSpecifier>? imports; // null means import all
  final bool isDeferred; // for deferred imports

  ImportStatement(this.path, {this.prefix, this.imports, this.isDeferred = false});

  @override
  String toString() {
    if (imports == null) {
      if (prefix != null) {
        return 'import "$path" as $prefix;';
      }
      return 'import "$path";';
    } else {
      final importList = imports!.map((i) => i.toString()).join(', ');
      return 'import "$path" show $importList;';
    }
  }
}

class ImportSpecifier extends ASTNode {
  final String name;
  final String? alias; // rename import

  ImportSpecifier(this.name, {this.alias});

  @override
  String toString() => alias != null ? '$name as $alias' : name;
}

class ExportStatement extends ASTNode {
  final List<String> exports;
  final String? fromPath; // re-export from another module

  ExportStatement(this.exports, {this.fromPath});

  @override
  String toString() {
    if (fromPath != null) {
      return 'export { ${exports.join(', ')} } from "$fromPath";';
    }
    return 'export { ${exports.join(', ')} };';
  }
}
