
import 'instruction.dart';

class StructInstance {
  final String typeName;
  final Map<String, dynamic> fields;
  final Map<String, String>? typeArguments; // Generic type arguments: T -> int, U -> string
  final List<String>? traitBounds; // Trait constraints for this instance

  StructInstance(this.typeName, this.fields, {this.typeArguments, this.traitBounds});

  dynamic getField(String name) {
    if (!fields.containsKey(name)) {
      throw Exception('Field $name not found in struct $typeName');
    }
    return fields[name];
  }

  dynamic safeGetField(String name) {
    return fields[name]; // Returns null if field doesn't exist
  }

  void setField(String name, dynamic value) {
    if (!fields.containsKey(name)) {
      throw Exception('Field $name not found in struct $typeName');
    }
    fields[name] = value;
  }

  bool hasField(String name) {
    return fields.containsKey(name);
  }

  String getConcreteTypeName() {
    if (typeArguments == null || typeArguments!.isEmpty) {
      return typeName;
    }
    final typeArgs = typeArguments!.values.join(', ');
    return '$typeName<$typeArgs>';
  }

  bool satisfiesTraitBound(String traitName) {
    return traitBounds?.contains(traitName) ?? false;
  }

  @override
  String toString() => '${getConcreteTypeName()}${fields.toString()}';
}

class GenericTypeDefinition {
  final String name;
  final List<String> typeParameters; // [T, U, V]
  final Map<String, String> fields; // field -> type (may contain type parameters)
  final Map<String, List<String>>? parameterBounds; // T -> [Clone, Eq]

  GenericTypeDefinition(this.name, this.typeParameters, this.fields, {this.parameterBounds});

  StructInstance instantiate(Map<String, String> typeArguments, Map<String, dynamic> fieldValues) {
    // Validate type arguments
    for (final param in typeParameters) {
      if (!typeArguments.containsKey(param)) {
        throw Exception('Missing type argument for parameter $param');
      }
    }
    for (final provided in typeArguments.keys) {
      if (!typeParameters.contains(provided)) {
        throw Exception('Unknown type parameter $provided');
      }
    }

    // Check trait bounds
    if (parameterBounds != null) {
      for (final param in typeParameters) {
        final bounds = parameterBounds![param];
        if (bounds != null && bounds.isNotEmpty) {
          // TODO: Validate that typeArguments[param] satisfies bounds
        }
      }
    }

    return StructInstance(
      name,
      fieldValues,
      typeArguments: typeArguments,
      traitBounds: parameterBounds?.values.expand((x) => x).toList(),
    );
  }
}

class TypeRegistry {
  // Generic types
  final Map<String, GenericTypeDefinition> _genericTypes = {};
  final Map<String, Map<String, String>> _concreteTypes = {}; // Box<int> -> {T: int}

  // Struct definitions
  final Map<String, Map<String, String>> _structRegistry = {}; // { structName: { fieldName: type } }

  // Interface/Trait definitions
  final Map<String, List<Map<String, dynamic>>> _interfaceRegistry = {}; // { interfaceName: [ {name, params, returnType} ] }
  final Map<String, Map<String, dynamic>> _genericInterfaceRegistry = {}; // Generic interfaces

  // Trait implementations
  final Map<String, Map<String, List<Instruction>>> _traitImpls = {}; // { structName: { methodName: instructions } }
  final Map<String, Map<String, List<Instruction>>> _staticTraitImpls = {}; // { structName: { methodName: instructions } }
  final Map<String, Map<String, Map<String, List<Instruction>>>> _genericTraitImpls = {}; // Generic trait implementations

  // Enum definitions
  final Map<String, Map<String, List<String>>> _enumRegistry = {}; // enum name -> variant name -> field types

  // Generic type methods
  void registerGenericType(GenericTypeDefinition definition) {
    _genericTypes[definition.name] = definition;
  }

  GenericTypeDefinition? getGenericType(String name) {
    return _genericTypes[name];
  }

  String registerConcreteType(String baseName, Map<String, String> typeArgs) {
    final concreteName = '$baseName<${typeArgs.values.join(', ')}>';
    _concreteTypes[concreteName] = typeArgs;
    return concreteName;
  }

  Map<String, String>? getTypeArguments(String concreteName) {
    return _concreteTypes[concreteName];
  }

  bool isGenericType(String name) {
    return _genericTypes.containsKey(name);
  }

  bool isConcreteType(String name) {
    return _concreteTypes.containsKey(name);
  }

  // Struct registry methods
  void registerStruct(String name, Map<String, String> fields) {
    _structRegistry[name] = fields;
  }

  void registerGenericStruct(String name, List<String> typeParameters, Map<String, String> fields, Map<String, List<String>>? parameterBounds) {
    // For now, just register the base struct
    // In a full implementation, we'd store the generic information separately
    _structRegistry[name] = fields;

    // Store as a generic type definition
    final definition = GenericTypeDefinition(
      name,
      typeParameters,
      fields,
      parameterBounds: parameterBounds,
    );
    registerGenericType(definition);
  }

  Map<String, String>? getStructFields(String name) {
    return _structRegistry[name];
  }

  bool hasStruct(String name) {
    return _structRegistry.containsKey(name);
  }

  Map<String, Map<String, String>> get structRegistry => Map.unmodifiable(_structRegistry);

  // Interface registry methods
  void registerInterface(String name, List<Map<String, dynamic>> methods) {
    _interfaceRegistry[name] = methods;
  }

  List<Map<String, dynamic>>? getInterfaceMethods(String name) {
    return _interfaceRegistry[name];
  }

  bool hasInterface(String name) {
    return _interfaceRegistry.containsKey(name);
  }

  Map<String, List<Map<String, dynamic>>> get interfaceRegistry => Map.unmodifiable(_interfaceRegistry);

  void registerGenericInterface(String name, Map<String, dynamic> definition) {
    _genericInterfaceRegistry[name] = definition;
  }

  Map<String, dynamic>? getGenericInterface(String name) {
    return _genericInterfaceRegistry[name];
  }

  Map<String, Map<String, dynamic>> get genericInterfaceRegistry => Map.unmodifiable(_genericInterfaceRegistry);

  // Trait implementation methods
  void registerTraitImpl(String structName, String methodName, List<Instruction> instructions) {
    _traitImpls.putIfAbsent(structName, () => {});
    _traitImpls[structName]![methodName] = instructions;
  }

  void registerTraitImpls(String structName, Map<String, List<Instruction>> methods) {
    _traitImpls[structName] = methods;
  }

  void registerTraitImplFromMaps(String structName, String interfaceName, Map<String, dynamic> methodsRaw, Map<String, dynamic> staticRaw) {
    // Instance methods
    final methodImplementations = <String, List<Instruction>>{};
    for (final entry in methodsRaw.entries) {
      if (entry.value is List<Instruction>) {
        methodImplementations[entry.key] = entry.value as List<Instruction>;
      } else {
        // Handle legacy format if needed
        final instrList = List<Map<String, dynamic>>.from(entry.value);
        final instructions = instrList.map((i) => Instruction(i['op'])).toList();
        methodImplementations[entry.key] = instructions;
      }
    }

    // Static methods
    final staticImplsForStruct = <String, List<Instruction>>{};
    for (final entry in staticRaw.entries) {
      if (entry.value is List<Instruction>) {
        staticImplsForStruct[entry.key] = entry.value as List<Instruction>;
      } else {
        // Handle legacy format if needed
        final instrList = List<Map<String, dynamic>>.from(entry.value);
        final instructions = instrList.map((i) => Instruction(i['op'])).toList();
        staticImplsForStruct[entry.key] = instructions;
      }
    }

    _traitImpls[structName] = methodImplementations;
    _staticTraitImpls[structName] = staticImplsForStruct;
  }

  List<Instruction>? getTraitImpl(String structName, String methodName) {
    return _traitImpls[structName]?[methodName];
  }

  Map<String, List<Instruction>>? getStructTraitImpls(String structName) {
    return _traitImpls[structName];
  }

  Map<String, Map<String, List<Instruction>>> get traitImpls => Map.unmodifiable(_traitImpls);

  void registerStaticTraitImpl(String structName, String methodName, List<Instruction> instructions) {
    _staticTraitImpls.putIfAbsent(structName, () => {});
    _staticTraitImpls[structName]![methodName] = instructions;
  }

  void registerStaticTraitImpls(String structName, Map<String, List<Instruction>> methods) {
    _staticTraitImpls[structName] = methods;
  }

  List<Instruction>? getStaticTraitImpl(String structName, String methodName) {
    return _staticTraitImpls[structName]?[methodName];
  }

  Map<String, List<Instruction>>? getStructStaticTraitImpls(String structName) {
    return _staticTraitImpls[structName];
  }

  Map<String, Map<String, List<Instruction>>> get staticTraitImpls => Map.unmodifiable(_staticTraitImpls);

  void registerGenericTraitImpl(String structName, String methodName, String typeSignature, List<Instruction> instructions) {
    _genericTraitImpls.putIfAbsent(structName, () => {});
    _genericTraitImpls[structName]!.putIfAbsent(methodName, () => {});
    _genericTraitImpls[structName]![methodName]![typeSignature] = instructions;
  }

  List<Instruction>? getGenericTraitImpl(String structName, String methodName, String typeSignature) {
    return _genericTraitImpls[structName]?[methodName]?[typeSignature];
  }

  List<Instruction>? getGenericTraitImplWithTypeArgs(String structName, String methodName, Map<String, String> typeArgs) {
    // Convert type arguments to signature string
    final typeSignature = typeArgs.entries.map((e) => '${e.key}=${e.value}').join(',');
    return getGenericTraitImpl(structName, methodName, typeSignature);
  }

  Map<String, Map<String, List<Instruction>>>? getStructGenericTraitImpls(String structName) {
    return _genericTraitImpls[structName];
  }

  Map<String, Map<String, Map<String, List<Instruction>>>> get genericTraitImpls => Map.unmodifiable(_genericTraitImpls);

  // Enum registry methods
  void registerEnum(String name, Map<String, List<String>> variants) {
    _enumRegistry[name] = variants;
  }

  Map<String, List<String>>? getEnumVariants(String name) {
    return _enumRegistry[name];
  }

  bool hasEnum(String name) {
    return _enumRegistry.containsKey(name);
  }

  Map<String, Map<String, List<String>>> get enumRegistry => Map.unmodifiable(_enumRegistry);

  // Copy methods for VM context sharing
  void copyTo(TypeRegistry other) {
    other._genericTypes.addAll(_genericTypes);
    other._concreteTypes.addAll(_concreteTypes);
    other._structRegistry.addAll(_structRegistry);
    other._interfaceRegistry.addAll(_interfaceRegistry);
    other._genericInterfaceRegistry.addAll(_genericInterfaceRegistry);
    other._traitImpls.addAll(_traitImpls);
    other._staticTraitImpls.addAll(_staticTraitImpls);
    other._genericTraitImpls.addAll(_genericTraitImpls);
    other._enumRegistry.addAll(_enumRegistry);
  }
}

class MapInstance {
  final Map<dynamic, dynamic> _data = {};
  final String keyType;
  final String valueType;

  MapInstance(this.keyType, this.valueType);

  dynamic get(dynamic key) {
    return _data[key];
  }

  void set(dynamic key, dynamic value) {
    _data[key] = value;
  }

  bool containsKey(dynamic key) {
    return _data.containsKey(key);
  }

  void remove(dynamic key) {
    _data.remove(key);
  }

  List<dynamic> keys() {
    return _data.keys.toList();
  }

  List<dynamic> values() {
    return _data.values.toList();
  }

  int size() {
    return _data.length;
  }

  bool isEmpty() {
    return _data.isEmpty;
  }

  void clear() {
    _data.clear();
  }

  String getTypeName() {
    return 'Map<$keyType, $valueType>';
  }

  @override
  String toString() => '${getTypeName()}${_data.toString()}';
}

class EnumInstance {
  final String enumName;
  final String variant;
  final List<dynamic>? values;

  EnumInstance(this.enumName, this.variant, {this.values});

  bool isVariant(String variantName) {
    return variant == variantName;
  }

  dynamic getValue(int index) {
    if (values == null || index >= values!.length) {
      throw Exception('Index $index out of bounds for enum variant $variant');
    }
    return values![index];
  }

  int getValueCount() {
    return values?.length ?? 0;
  }

  @override
  String toString() => values != null
      ? '$enumName::$variant(${values!.join(', ')})'
      : '$enumName::$variant';
}



// Old OpCode enum removed - now using sealed classes from instruction_new.dart