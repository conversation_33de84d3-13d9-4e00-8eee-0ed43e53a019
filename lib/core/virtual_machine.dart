import 'dart:io';

import 'instruction.dart';
import 'opcode.dart' as opcodes;
import 'bytecode.dart';
import 'runtime.dart';

class _ReturnException implements Exception {
  final dynamic value;

  _ReturnException(this.value);
}

class _MethodReference {
  final StructInstance instance;
  final String methodName;

  _MethodReference(this.instance, this.methodName);

  @override
  String toString() => '${instance.typeName}.$methodName';
}

class _TryBlock {
  final int startPC;
  final List<_CatchHandler> catchHandlers;
  final int? finallyPC;

  _TryBlock(this.startPC, this.catchHandlers, {this.finallyPC});
}

class _CatchHandler {
  final String? exceptionType;
  final int handlerPC;

  _CatchHandler(this.exceptionType, this.handlerPC);
}

class VirtualMachine {
  bool _verbose = false;

  set verbose(bool value) => _verbose = value;

  List<dynamic> stack = [];
  Map<String, dynamic> vars = {};
  List<Instruction> code = [];
  int ip = 0;

  StructInstance? thisInstance;

  // Centralized type registry
  TypeRegistry typeRegistry = TypeRegistry();

  // Error handling
  List<_TryBlock> tryStack = []; // Stack of try blocks for error handling

  // Backward compatibility getters for tests
  Map<String, Map<String, List<Instruction>>> get staticTraitImpls => typeRegistry.staticTraitImpls;

  void load(Bytecode bytecode) {
    code = bytecode.instructions;
    ip = 0;
    stack.clear();
    vars.clear();
  }

  void run() {
    if (_verbose) print("Running VM with code: $code");
    while (ip < code.length) {
      final instr = code[ip++];
      _executeInstruction(instr.op);
    }
  }

  // Execute type-safe instructions
  void _executeInstruction(opcodes.OpCode op) {
    switch (op) {
      case opcodes.LoadConstOp(value: final value):
        stack.add(value);
        break;

      case opcodes.LoadOp(name: final name):
        if (name == 'null') {
          stack.add(null);
        } else if (vars.containsKey(name)) {
          stack.add(vars[name]);
        } else {
          throw Exception('Variable "$name" not defined');
        }
        break;

      case opcodes.StoreOp(name: final name):
        vars[name] = stack.removeLast();
        break;

      case opcodes.LoadThisOp():
        final thisInstance = vars['this'];
        if (thisInstance == null) {
          throw Exception('No "this" context in current scope');
        }
        stack.add(thisInstance);
        break;

      case opcodes.AddOp():
        final b = stack.removeLast();
        final a = stack.removeLast();
        stack.add(a + b);
        break;

      case opcodes.SubOp():
        final b = stack.removeLast();
        final a = stack.removeLast();
        stack.add(a - b);
        break;

      case opcodes.MulOp():
        final b = stack.removeLast();
        final a = stack.removeLast();
        stack.add(a * b);
        break;

      case opcodes.DivOp():
        final b = stack.removeLast();
        final a = stack.removeLast();
        stack.add(a ~/ b);
        break;
      case opcodes.ModOp():
        final b = stack.removeLast();
        final a = stack.removeLast();
        stack.add(a % b);
        break;

      case opcodes.PrintOp():
        final value = stack.removeLast();
        if (value is String && value.contains(r'${')) {
          final interpolated = _interpolate(value, vars);
          stdout.writeln(interpolated);
        } else {
          stdout.writeln(value);
        }
        break;

      case opcodes.CallOp(name: final name, argc: final argc):
        final func = vars[name];

        if (func is! Bytecode) {
          throw Exception('Cannot call non-function $name');
        }

        // Chuẩn bị máy ảo con để gọi hàm
        final vm = VirtualMachine();
        vm.verbose = _verbose;
        copyContextTo(vm);

        vm.load(func);

        // Pop argument từ stack (ngược chiều push)
        final argsList =
            List.generate(argc, (_) => stack.removeLast()).reversed.toList();

        // Gán tham số vào biến cục bộ của hàm
        for (var i = 0; i < argc; i++) {
          vm.vars['arg$i'] = argsList[i];
        }

        try {
          vm.run();
          // Nếu không có return -> push null để stack đồng nhất
          stack.add(null);
        } on _ReturnException catch (e) {
          stack.add(e.value); // đưa return value vào stack gọi
        }
        break;

      case opcodes.ReturnOp():
        final returnValue = stack.isNotEmpty ? stack.removeLast() : null;
        stack.clear(); // clear toàn bộ stack khi return
        throw _ReturnException(returnValue);

      case opcodes.NotOp():
        final value = stack.removeLast();
        if (value is! bool) {
          throw Exception(
            'NotOp expects bool, but got $value (${value.runtimeType})',
          );
        }
        stack.add(!value);
        break;

      case opcodes.NegOp():
        final value = stack.removeLast();
        stack.add(-value);
        break;

      case opcodes.PosOp():
        final value = stack.removeLast();
        stack.add(value);
        break;

      case opcodes.LessOp():
        final b = stack.removeLast();
        final a = stack.removeLast();
        stack.add(a < b);
        break;

      case opcodes.GreaterOp():
        final b = stack.removeLast();
        final a = stack.removeLast();
        stack.add(a > b);
        break;

      case opcodes.EqOp():
        final b = stack.removeLast();
        final a = stack.removeLast();
        stack.add(a == b);
        break;

      case opcodes.NeqOp():
        final b = stack.removeLast();
        final a = stack.removeLast();
        stack.add(a != b);
        break;

      case opcodes.LeqOp():
        final b = stack.removeLast();
        final a = stack.removeLast();
        stack.add(a <= b);
        break;

      case opcodes.GeqOp():
        final b = stack.removeLast();
        final a = stack.removeLast();
        stack.add(a >= b);
        break;
      case opcodes.JumpIfTrueOp(target: final target):
        final condition = stack.removeLast();
        if (condition) {
          ip = int.parse(target);
        }
        break;

      case opcodes.JumpOp(target: final target):
        // For resolved labels, target is already an index as string
        ip = int.parse(target);
        break;

      case opcodes.JumpIfFalseOp(target: final target):
        final condition = stack.removeLast();
        if (!condition) {
          ip = int.parse(target);
        }
        break;

      case opcodes.LabelOp(name: final name):
        // Labels are handled during bytecode resolution, no runtime action needed
        break;

      case opcodes.DefineStructOp(name: final name, fields: final fields):
        typeRegistry.registerStruct(name, fields);
        break;

      case opcodes.DefineGenericStructOp(
        name: final name,
        typeParameters: final typeParameters,
        fields: final fields,
        parameterBounds: final parameterBounds
      ):
        typeRegistry.registerGenericStruct(name, typeParameters, fields, parameterBounds);
        break;

      case opcodes.InstantiateGenericOp(
        baseName: final baseName,
        typeArguments: final typeArguments,
        fieldNames: final fieldNames
      ):
        final fieldValues = <String, dynamic>{};
        for (int i = fieldNames.length - 1; i >= 0; i--) {
          fieldValues[fieldNames[i]] = stack.removeLast();
        }

        final instance = StructInstance(baseName, fieldValues, typeArguments: typeArguments);
        stack.add(instance);
        break;

      case opcodes.DefineInterfaceOp(name: final name, methods: final methods):
        typeRegistry.registerInterface(name, methods);
        break;

      case opcodes.ImplTraitOp(
        structName: final struct,
        interfaceName: final trait,
        methods: final methodsRaw,
        staticMethods: final staticRaw
      ):
        // Use TypeRegistry to handle trait implementations
        typeRegistry.registerTraitImplFromMaps(struct, trait, methodsRaw, staticRaw);
        break;

      case opcodes.MakeStructOp(structName: final structName, fieldNames: final fieldNames):
        final fieldValues = <String, dynamic>{};
        for (int i = fieldNames.length - 1; i >= 0; i--) {
          fieldValues[fieldNames[i]] = stack.removeLast();
        }

        final instance = StructInstance(structName, fieldValues);
        stack.add(instance);
        break;

      case opcodes.GetFieldOp(field: final field):
        final instance = stack.removeLast();
        if (instance is! StructInstance) {
          throw Exception(
            'GET_FIELD requires StructInstance but got $instance',
          );
        }

        // Check if this is actually a method call disguised as field access
        final structName = instance.typeName;
        final methodImpls = typeRegistry.getStructTraitImpls(structName);
        if (methodImpls != null && methodImpls.containsKey(field)) {
          // This is a method, create a method reference
          stack.add(_MethodReference(instance, field));
        } else {
          // This is a regular field
          stack.add(instance.getField(field));
        }
        break;

      case opcodes.SetFieldOp(field: final field):
        final value = stack.removeLast();
        final instance = stack.removeLast();
        if (instance is! StructInstance) {
          throw Exception('SET_FIELD requires StructInstance');
        }
        instance.setField(field, value);
        break;

      case opcodes.MethodCallOp(method: final methodName, argc: final argc):
        // Handle method reference calls
        if (stack.length > argc && stack[stack.length - argc - 1] is _MethodReference) {
          final methodRef = stack.removeAt(stack.length - argc - 1) as _MethodReference;
          final instance = methodRef.instance;
          final actualMethodName = methodRef.methodName;

          final structName = instance.typeName;
          final methodCode = typeRegistry.getTraitImpl(structName, actualMethodName);
          if (methodCode == null) {
            throw Exception('Method $actualMethodName not found for $structName');
          }

          final vm = VirtualMachine();
          copyContextTo(vm);

          vm.load(Bytecode(methodCode));

          vm.thisInstance = instance;
          vm.vars['this'] = instance;

          final argsList =
              List.generate(argc, (_) => stack.removeLast()).reversed.toList();
          for (var i = 0; i < argc; i++) {
            vm.vars['arg$i'] = argsList[i];
          }

          try {
            vm.run();
            stack.add(null);
          } on _ReturnException catch (e) {
            stack.add(e.value);
          }
          break;
        }

        // Regular method call
        _executeMethodCall(methodName, argc);
        break;

      case opcodes.StaticCallOp(structName: final struct, method: final method, argc: final argc):
        final methodCode = typeRegistry.getStaticTraitImpl(struct, method);
        if (methodCode == null) {
          throw Exception(
            'Static method $method not found on struct $struct',
          );
        }

        final vm = VirtualMachine();
        vm.verbose = _verbose;
        copyContextTo(vm);

        vm.load(Bytecode(methodCode));

        final argsList =
            List.generate(argc, (_) => stack.removeLast()).reversed.toList();
        for (var i = 0; i < argc; i++) {
          vm.vars['arg$i'] = argsList[i];
        }

        try {
          vm.run();
          stack.add(null);
        } on _ReturnException catch (e) {
          stack.add(e.value);
        }
        break;

      case opcodes.LoadThisDupOp():
        if (thisInstance == null) {
          throw Exception('Cannot LOAD_THIS_DUP outside of method context');
        }
        stack.add(thisInstance!);
        break;

      case opcodes.ResolveGenericMethodOp(
        method: final methodName,
        argc: final argc,
        typeArguments: final typeArgs
      ):
        final instance = stack.removeAt(stack.length - argc - 1);
        if (instance is! StructInstance) {
          throw Exception('Expected struct instance for generic method call');
        }

        _executeGenericMethodCall(instance, methodName, argc, typeArgs);
        break;

      // Enum support
      case opcodes.DefineEnumOp(name: final name, variants: final variants):
        typeRegistry.registerEnum(name, variants);
        break;

      case opcodes.MakeEnumVariantOp(
        enumName: final enumName,
        variant: final variant,
        valueCount: final valueCount
      ):
        final values = valueCount > 0
            ? List.generate(valueCount, (_) => stack.removeLast()).reversed.toList()
            : null;

        stack.add(EnumInstance(enumName, variant, values: values));
        break;

      // Map support
      case opcodes.MakeMapOp(keyType: final keyType, valueType: final valueType):
        stack.add(MapInstance(keyType, valueType));
        break;

      case opcodes.MapGetOp():
        final key = stack.removeLast();
        final map = stack.removeLast();
        if (map is! MapInstance) {
          throw Exception('MAP_GET requires MapInstance');
        }
        stack.add(map.get(key));
        break;

      case opcodes.MapSetOp():
        final value = stack.removeLast();
        final key = stack.removeLast();
        final map = stack.removeLast();
        if (map is! MapInstance) {
          throw Exception('MAP_SET requires MapInstance');
        }
        map.set(key, value);
        stack.add(map);
        break;

      case opcodes.MapContainsKeyOp():
        final key = stack.removeLast();
        final map = stack.removeLast();
        if (map is! MapInstance) {
          throw Exception('MAP_CONTAINS_KEY requires MapInstance');
        }
        stack.add(map.containsKey(key));
        break;

      default:
        throw Exception('Unhandled OpCode: ${op.runtimeType}. This OpCode has not been migrated to the new system yet.');
      }
    }


  void copyContextTo(VirtualMachine vm) {
    vm.typeRegistry = typeRegistry;
  }

  void _executeMethodCall(String methodName, int argc) {
    final instance = stack.removeAt(stack.length - argc - 1);
    if (instance is! StructInstance) {
      throw Exception('Expected struct instance for method call');
    }

    final structName = instance.typeName;
    final methodCode = typeRegistry.getTraitImpl(structName, methodName);
    if (methodCode == null) {
      throw Exception('Method $methodName not found for $structName');
    }

    final vm = VirtualMachine();
    copyContextTo(vm);

    vm.load(Bytecode(methodCode));
    vm.thisInstance = instance;
    vm.vars['this'] = instance;

    final argsList = List.generate(argc, (_) => stack.removeLast()).reversed.toList();
    for (var i = 0; i < argc; i++) {
      vm.vars['arg$i'] = argsList[i];
    }

    try {
      vm.run();
      stack.add(null);
    } on _ReturnException catch (e) {
      stack.add(e.value);
    }
  }

  void _executeGenericMethodCall(StructInstance instance, String methodName, int argc, Map<String, String> typeArgs) {
    final structName = instance.typeName;
    final methodCode = typeRegistry.getGenericTraitImplWithTypeArgs(structName, methodName, typeArgs);

    if (methodCode != null) {
      final vm = VirtualMachine();
      copyContextTo(vm);

      vm.load(Bytecode(methodCode));
      vm.thisInstance = instance;
      vm.vars['this'] = instance;

      // Add type arguments to VM context
      for (final entry in typeArgs.entries) {
        vm.vars['type_${entry.key}'] = entry.value;
      }

      final argsList = List.generate(argc, (_) => stack.removeLast()).reversed.toList();
      for (var i = 0; i < argc; i++) {
        vm.vars['arg$i'] = argsList[i];
      }

      try {
        vm.run();
        stack.add(null);
      } on _ReturnException catch (e) {
        stack.add(e.value);
      }
      return;
    }

    // Fallback to regular method call
    stack.insert(stack.length - argc, instance);
    _executeMethodCall(methodName, argc);
  }

  @override
  String toString() {
    return 'VirtualMachine{stack: $stack, vars: $vars, ip: $ip, typeRegistry: $typeRegistry}';
  }

  String _interpolate(String template, Map<String, dynamic> scope) {
    return template.replaceAllMapped(RegExp(r'\$\{([^}]+)\}'), (match) {
      final expr = match.group(1)!;
      if (expr.startsWith('this.')) {
        final field = expr.substring(5);
        final thisInstance = scope['this'];
        if (thisInstance is StructInstance &&
            thisInstance.fields.containsKey(field)) {
          return thisInstance.fields[field].toString();
        }
      }
      return match.group(0)!; // fallback giữ nguyên
    });
  }
}
